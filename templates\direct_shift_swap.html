{% extends "base.html" %}

{% block title %}ALEMIS - تبديل دوام مباشر{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <h2 class="mb-4">تبديل دوام مباشر</h2>

        <!-- تنبيه حالة الجدول -->
        {% if schedule_status != 'draft' %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> لا يمكن إجراء تبديل مباشر للدوام لأن الجدول تم اعتماده. يرجى استخدام نظام طلبات التبديل الرسمي.
        </div>
        {% endif %}

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-exchange-alt me-1"></i>
                تبديل دوام مباشر لشهر {{ month_name }} {{ current_year }}
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> يمكنك تبديل الدوام مباشرة مع زملائك قبل اعتماد الجدول من قبل المدير. بعد الاعتماد، يجب استخدام نظام طلبات التبديل الرسمي.
                </div>

                <form id="direct-swap-form" method="POST" action="{{ url_for('direct_shift_swap') }}">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="swap_type" class="form-label">نوع التبديل</label>
                                <select class="form-select" id="swap_type" name="swap_type" required {% if schedule_status != 'draft' %}disabled{% endif %}>
                                    <option value="">-- اختر نوع التبديل --</option>
                                    <option value="shift_swap">تبديل شفت محدد</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="swap_with_user_id" class="form-label">التبديل مع الموظف</label>
                                <select class="form-select" id="swap_with_user_id" name="swap_with_user_id" required {% if schedule_status != 'draft' %}disabled{% endif %}>
                                    <option value="">-- اختر الموظف --</option>
                                    {% for employee in employees %}
                                    {% if employee.id != session.user_id %}
                                    <option value="{{ employee.id }}">{{ employee.first_name }} {{ employee.last_name }}</option>
                                    {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div id="day_swap_section" style="display: none;">
                        <h5 class="mb-3">تبديل أيام متعددة</h5>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            يمكنك اختيار عدة أيام للتبديل في نفس الطلب. اضغط على الأيام المطلوبة مع الضغط على Ctrl للاختيار المتعدد.
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="my_days" class="form-label">أيامي المراد تبديلها</label>
                                    <select class="form-select" id="my_days" name="my_days" multiple size="8" {% if schedule_status != 'draft' %}disabled{% endif %}>
                                        {% for day in range(1, num_days + 1) %}
                                        <option value="{{ day }}">{{ day }} {{ month_name }} -
                                            {% if day_of_week[day-1] == 0 %}
                                            السبت
                                            {% elif day_of_week[day-1] == 1 %}
                                            الأحد
                                            {% elif day_of_week[day-1] == 2 %}
                                            الإثنين
                                            {% elif day_of_week[day-1] == 3 %}
                                            الثلاثاء
                                            {% elif day_of_week[day-1] == 4 %}
                                            الأربعاء
                                            {% elif day_of_week[day-1] == 5 %}
                                            الخميس
                                            {% elif day_of_week[day-1] == 6 %}
                                            الجمعة
                                            {% endif %}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <small class="form-text text-muted">اضغط Ctrl + النقر لاختيار عدة أيام</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="other_days" class="form-label">الأيام المراد التبديل معها</label>
                                    <select class="form-select" id="other_days" name="other_days" multiple size="8" {% if schedule_status != 'draft' %}disabled{% endif %}>
                                        {% for day in range(1, num_days + 1) %}
                                        <option value="{{ day }}">{{ day }} {{ month_name }} -
                                            {% if day_of_week[day-1] == 0 %}
                                            السبت
                                            {% elif day_of_week[day-1] == 1 %}
                                            الأحد
                                            {% elif day_of_week[day-1] == 2 %}
                                            الإثنين
                                            {% elif day_of_week[day-1] == 3 %}
                                            الثلاثاء
                                            {% elif day_of_week[day-1] == 4 %}
                                            الأربعاء
                                            {% elif day_of_week[day-1] == 5 %}
                                            الخميس
                                            {% elif day_of_week[day-1] == 6 %}
                                            الجمعة
                                            {% endif %}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <small class="form-text text-muted">اضغط Ctrl + النقر لاختيار عدة أيام</small>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> يجب أن يكون عدد الأيام المختارة متساوياً في كلا الجانبين.
                        </div>
                    </div>

                    <div id="shift_swap_section" style="display: none;">
                        <h5 class="mb-3">تبديل شفت محدد</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="my_day_shift" class="form-label">اليوم الخاص بي</label>
                                    <select class="form-select" id="my_day_shift" name="my_day_shift" {% if schedule_status != 'draft' %}disabled{% endif %}>
                                        <option value="">-- اختر اليوم --</option>
                                        {% for day in range(1, num_days + 1) %}
                                        <option value="{{ day }}">{{ day }} {{ month_name }} -
                                            {% if day_of_week[day-1] == 0 %}
                                            السبت
                                            {% elif day_of_week[day-1] == 1 %}
                                            الأحد
                                            {% elif day_of_week[day-1] == 2 %}
                                            الإثنين
                                            {% elif day_of_week[day-1] == 3 %}
                                            الثلاثاء
                                            {% elif day_of_week[day-1] == 4 %}
                                            الأربعاء
                                            {% elif day_of_week[day-1] == 5 %}
                                            الخميس
                                            {% elif day_of_week[day-1] == 6 %}
                                            الجمعة
                                            {% endif %}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="my_shift" class="form-label">الشفت الخاص بي</label>
                                    <select class="form-select" id="my_shift" name="my_shift" {% if schedule_status != 'draft' %}disabled{% endif %}>
                                        <option value="">-- اختر الشفت --</option>
                                        <option value="morning">صباحي (8ص-4م)</option>
                                        <option value="evening">مسائي (4م-12ل)</option>
                                        <option value="night">ليلي (12ل-8ص)</option>
                                        <option value="split">فترتين (9ص-12ظ، 4م-9م)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="other_day_shift" class="form-label">اليوم المراد التبديل معه</label>
                                    <select class="form-select" id="other_day_shift" name="other_day_shift" {% if schedule_status != 'draft' %}disabled{% endif %}>
                                        <option value="">-- اختر اليوم --</option>
                                        {% for day in range(1, num_days + 1) %}
                                        <option value="{{ day }}">{{ day }} {{ month_name }} -
                                            {% if day_of_week[day-1] == 0 %}
                                            السبت
                                            {% elif day_of_week[day-1] == 1 %}
                                            الأحد
                                            {% elif day_of_week[day-1] == 2 %}
                                            الإثنين
                                            {% elif day_of_week[day-1] == 3 %}
                                            الثلاثاء
                                            {% elif day_of_week[day-1] == 4 %}
                                            الأربعاء
                                            {% elif day_of_week[day-1] == 5 %}
                                            الخميس
                                            {% elif day_of_week[day-1] == 6 %}
                                            الجمعة
                                            {% endif %}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="other_shift" class="form-label">الشفت المراد التبديل معه</label>
                                    <select class="form-select" id="other_shift" name="other_shift" {% if schedule_status != 'draft' %}disabled{% endif %}>
                                        <option value="">-- اختر الشفت --</option>
                                        <option value="morning">صباحي (8ص-4م)</option>
                                        <option value="evening">مسائي (4م-12ل)</option>
                                        <option value="night">ليلي (12ل-8ص)</option>
                                        <option value="split">فترتين (9ص-12ظ، 4م-9م)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب التبديل</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required {% if schedule_status != 'draft' %}disabled{% endif %}></textarea>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" {% if schedule_status != 'draft' %}disabled{% endif %}>
                            <i class="fas fa-paper-plane me-1"></i>
                            إرسال طلب التبديل
                        </button>
                        <a href="{{ url_for('my_shift_schedule') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة إلى جدول الدوام
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const swapTypeSelect = document.getElementById('swap_type');
        const daySwapSection = document.getElementById('day_swap_section');
        const shiftSwapSection = document.getElementById('shift_swap_section');

        // تعيين القيمة الافتراضية لنوع التبديل وإظهار القسم المناسب
        swapTypeSelect.value = 'shift_swap';
        daySwapSection.style.display = 'none';
        shiftSwapSection.style.display = 'block';

        // تعيين الحقول المطلوبة
        document.getElementById('my_day_shift').setAttribute('required', 'required');
        document.getElementById('my_shift').setAttribute('required', 'required');
        document.getElementById('other_day_shift').setAttribute('required', 'required');
        document.getElementById('other_shift').setAttribute('required', 'required');

        // إظهار/إخفاء أقسام النموذج حسب نوع التبديل
        swapTypeSelect.addEventListener('change', function() {
            if (this.value === 'shift_swap') {
                daySwapSection.style.display = 'none';
                shiftSwapSection.style.display = 'block';

                // تعيين الحقول المطلوبة
                document.getElementById('my_days').removeAttribute('required');
                document.getElementById('other_days').removeAttribute('required');
                document.getElementById('my_day_shift').setAttribute('required', 'required');
                document.getElementById('my_shift').setAttribute('required', 'required');
                document.getElementById('other_day_shift').setAttribute('required', 'required');
                document.getElementById('other_shift').setAttribute('required', 'required');
            } else if (this.value === 'day_swap') {
                daySwapSection.style.display = 'block';
                shiftSwapSection.style.display = 'none';

                // تعيين الحقول المطلوبة
                document.getElementById('my_days').setAttribute('required', 'required');
                document.getElementById('other_days').setAttribute('required', 'required');
                document.getElementById('my_day_shift').removeAttribute('required');
                document.getElementById('my_shift').removeAttribute('required');
                document.getElementById('other_day_shift').removeAttribute('required');
                document.getElementById('other_shift').removeAttribute('required');
            } else {
                daySwapSection.style.display = 'none';
                shiftSwapSection.style.display = 'none';

                // إزالة جميع الحقول المطلوبة
                document.getElementById('my_days').removeAttribute('required');
                document.getElementById('other_days').removeAttribute('required');
                document.getElementById('my_day_shift').removeAttribute('required');
                document.getElementById('my_shift').removeAttribute('required');
                document.getElementById('other_day_shift').removeAttribute('required');
                document.getElementById('other_shift').removeAttribute('required');
            }
        });

        // التحقق من النموذج قبل الإرسال
        document.getElementById('direct-swap-form').addEventListener('submit', function(e) {
            const swapType = swapTypeSelect.value;

            if (swapType === 'shift_swap') {
                const myDayShift = document.getElementById('my_day_shift').value;
                const otherDayShift = document.getElementById('other_day_shift').value;
                const myShift = document.getElementById('my_shift').value;
                const otherShift = document.getElementById('other_shift').value;

                if (myDayShift === otherDayShift && myShift === otherShift) {
                    e.preventDefault();
                    alert('لا يمكن تبديل نفس الشفت في نفس اليوم');
                    return;
                }
            } else if (swapType === 'day_swap') {
                const myDays = Array.from(document.getElementById('my_days').selectedOptions).map(option => option.value);
                const otherDays = Array.from(document.getElementById('other_days').selectedOptions).map(option => option.value);

                if (myDays.length === 0 || otherDays.length === 0) {
                    e.preventDefault();
                    alert('يرجى اختيار الأيام المراد تبديلها');
                    return;
                }

                if (myDays.length !== otherDays.length) {
                    e.preventDefault();
                    alert('يجب أن يكون عدد الأيام المختارة متساوياً في كلا الجانبين');
                    return;
                }

                // التحقق من عدم وجود تداخل في الأيام
                const commonDays = myDays.filter(day => otherDays.includes(day));
                if (commonDays.length > 0) {
                    e.preventDefault();
                    alert('لا يمكن أن تكون نفس الأيام في كلا الجانبين');
                    return;
                }
            }
        });
    });
</script>
{% endblock %}
