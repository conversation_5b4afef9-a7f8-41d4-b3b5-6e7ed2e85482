from flask import Flask, render_template, redirect, url_for, flash, request, jsonify, session, g
import sqlite3
from datetime import datetime, timedelta, date
import os
import secrets
import hashlib
import calendar
from werkzeug.utils import secure_filename

# استيراد مكتبة WeasyPrint للتصدير إلى PDF
try:
    from weasyprint import HTML, CSS
    from weasyprint.text.fonts import FontConfiguration
    WEASYPRINT_AVAILABLE = True
except ImportError:
    print("⚠️ تحذير: WeasyPrint غير متاح. سيتم تعطيل تصدير PDF.")
    WEASYPRINT_AVAILABLE = False

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)
app.config['DATABASE'] = 'alemis.db'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create uploads directory if it doesn't exist
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

# Allowed file extensions
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Database functions
def get_db():
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect(app.config['DATABASE'])
        db.row_factory = sqlite3.Row
    return db

def init_db():
    with app.app_context():
        db = get_db()
        with open('schema.sql', 'r', encoding='utf-8') as f:
            db.executescript(f.read())
        db.commit()

def query_db(query, args=(), one=False):
    cur = get_db().execute(query, args)
    rv = cur.fetchall()
    cur.close()
    return (rv[0] if rv else None) if one else rv

@app.teardown_appcontext
def close_connection(exception):
    db = getattr(g, '_database', None)
    if db is not None:
        db.close()

# Helper functions - Password encryption (SHA256 مؤقتاً)
def hash_password(password):
    """تشفير كلمة المرور باستخدام SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def check_password(hashed_password, password):
    """التحقق من كلمة المرور"""
    return hashed_password == hashlib.sha256(password.encode()).hexdigest()

def is_authenticated():
    return 'user_id' in session

def get_current_user():
    if 'user_id' in session:
        return query_db('SELECT * FROM users WHERE id = ?', [session['user_id']], one=True)
    return None

def ensure_leave_balances(user_id):
    # Get leave types
    leave_types = query_db('SELECT * FROM leave_types')

    # Get current year
    current_year = datetime.now().year

    # Check if balances exist for the user for the current year
    db = get_db()

    for leave_type in leave_types:
        # Check if balance exists
        balance = query_db('''
            SELECT * FROM leave_balances
            WHERE user_id = ? AND leave_type_id = ? AND year = ?
        ''', [user_id, leave_type['id'], current_year], one=True)

        if not balance:
            # Create balance
            db.execute('''
                INSERT INTO leave_balances
                (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', [user_id, leave_type['id'], current_year, leave_type['default_days'], 0, leave_type['default_days']])

    db.commit()

def use_coverage_compensation_days(user_id, days):
    """استخدام أيام بدل التغطية"""
    db = get_db()
    current_year = datetime.now().year

    # Get coverage compensation leave type
    coverage_leave_type = query_db('SELECT * FROM leave_types WHERE name = "بدل يوم تغطية"', one=True)
    if not coverage_leave_type:
        return False

    # Get current balance
    balance = query_db('''
        SELECT * FROM leave_balances
        WHERE user_id = ? AND leave_type_id = ? AND year = ?
    ''', [user_id, coverage_leave_type['id'], current_year], one=True)

    if balance and balance['remaining_days'] >= days:
        # Update balance
        db.execute('''
            UPDATE leave_balances
            SET used_days = used_days + ?, remaining_days = remaining_days - ?
            WHERE user_id = ? AND leave_type_id = ? AND year = ?
        ''', [days, days, user_id, coverage_leave_type['id'], current_year])
        db.commit()
        return True

    return False

# Routes
@app.route('/')
def index():
    if is_authenticated():
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if is_authenticated():
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = query_db('SELECT * FROM users WHERE username = ?', [username], one=True)

        if user and check_password(user['password_hash'], password):
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role'] = user['role']

            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('فشل تسجيل الدخول. الرجاء التحقق من اسم المستخدم وكلمة المرور', 'danger')

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('index'))

@app.route('/interactive_dashboard')
def interactive_dashboard():
    """لوحة المعلومات التفاعلية مع الرسوم البيانية"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى لوحة المعلومات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # إحصائيات عامة
    stats = {
        'total_employees': query_db('SELECT COUNT(*) as count FROM users WHERE role = "employee" AND is_active = 1', one=True)['count'],
        'on_leave_today': query_db('''
            SELECT COUNT(DISTINCT u.id) as count
            FROM users u
            JOIN leave_requests lr ON u.id = lr.user_id
            WHERE lr.status = 'approved'
            AND ? BETWEEN lr.start_date AND lr.end_date
        ''', [datetime.now().strftime('%Y-%m-%d')], one=True)['count'],
        'pending_requests': query_db('SELECT COUNT(*) as count FROM leave_requests WHERE status = "pending"', one=True)['count'],
        'monthly_requests': query_db('''
            SELECT COUNT(*) as count FROM leave_requests
            WHERE strftime('%Y-%m', created_at) = ?
        ''', [datetime.now().strftime('%Y-%m')], one=True)['count']
    }

    # بيانات أنواع الإجازات
    leave_types_query = query_db('''
        SELECT lt.name, COUNT(lr.id) as count
        FROM leave_types lt
        LEFT JOIN leave_requests lr ON lt.id = lr.leave_type_id
        GROUP BY lt.id, lt.name
    ''')

    leave_types_data = {
        'labels': [row['name'] for row in leave_types_query],
        'data': [row['count'] for row in leave_types_query]
    }

    # بيانات الطلبات الشهرية (آخر 6 أشهر)
    monthly_requests_query = query_db('''
        SELECT strftime('%Y-%m', created_at) as month, COUNT(*) as count
        FROM leave_requests
        WHERE created_at >= date('now', '-6 months')
        GROUP BY strftime('%Y-%m', created_at)
        ORDER BY month
    ''')

    monthly_requests_data = {
        'labels': [row['month'] for row in monthly_requests_query],
        'data': [row['count'] for row in monthly_requests_query]
    }

    # بيانات حالات الطلبات
    status_query = query_db('''
        SELECT status, COUNT(*) as count
        FROM leave_requests
        GROUP BY status
    ''')

    request_status_data = {
        'labels': [row['status'] for row in status_query],
        'data': [row['count'] for row in status_query]
    }

    # بيانات الأقسام
    department_query = query_db('''
        SELECT d.name, COUNT(lr.id) as count
        FROM departments d
        LEFT JOIN users u ON d.id = u.department_id
        LEFT JOIN leave_requests lr ON u.id = lr.user_id
        GROUP BY d.id, d.name
    ''')

    department_data = {
        'labels': [row['name'] for row in department_query],
        'data': [row['count'] for row in department_query]
    }

    # آخر الطلبات
    recent_requests = query_db('''
        SELECT lr.*, u.first_name || ' ' || u.last_name as employee_name,
               lt.name as request_type
        FROM leave_requests lr
        JOIN users u ON lr.user_id = u.id
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        ORDER BY lr.created_at DESC
        LIMIT 10
    ''')

    import json
    return render_template('interactive_dashboard.html',
                         user=user,
                         stats=stats,
                         leave_types_data=json.dumps(leave_types_data),
                         monthly_requests_data=json.dumps(monthly_requests_data),
                         request_status_data=json.dumps(request_status_data),
                         department_data=json.dumps(department_data),
                         recent_requests=recent_requests)

@app.route('/dashboard')
def dashboard():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى لوحة التحكم', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # For regular employees, show only their own data
    if user['role'] == 'employee':
        # Get leave requests for the user
        leave_requests = query_db('''
            SELECT lr.*, lt.name as leave_type_name
            FROM leave_requests lr
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            WHERE lr.user_id = ?
            ORDER BY lr.created_at DESC
            LIMIT 5
        ''', [user['id']])

        # Get coverage requests for the user
        coverage_requests = query_db('''
            SELECT cr.*, u.first_name, u.last_name
            FROM coverage_requests cr
            JOIN users u ON cr.user_id = u.id
            WHERE cr.user_id = ?
            ORDER BY cr.created_at DESC
            LIMIT 5
        ''', [user['id']])

        # Get leave balances for the user
        leave_balances = query_db('''
            SELECT lb.*, lt.name as leave_type_name
            FROM leave_balances lb
            JOIN leave_types lt ON lb.leave_type_id = lt.id
            WHERE lb.user_id = ? AND lb.year = ?
        ''', [user['id'], datetime.now().year])

        return render_template('dashboard.html', user=user, leave_requests=leave_requests,
                              coverage_requests=coverage_requests, leave_balances=leave_balances)

    # For managers, HR, and GM, show supervisory data
    else:
        # Get pending leave requests that need approval
        if user['role'] == 'manager':
            # Managers see requests from their department
            pending_leave_requests = query_db('''
                SELECT lr.*, lt.name as leave_type_name, u.first_name, u.last_name, u.username
                FROM leave_requests lr
                JOIN leave_types lt ON lr.leave_type_id = lt.id
                JOIN users u ON lr.user_id = u.id
                WHERE lr.status = 'pending' AND u.department_id = ?
                ORDER BY lr.created_at DESC
                LIMIT 10
            ''', [user['department_id']])
        else:
            # Admin, HR, and GM see all pending requests
            pending_leave_requests = query_db('''
                SELECT lr.*, lt.name as leave_type_name, u.first_name, u.last_name, u.username, d.name as department_name
                FROM leave_requests lr
                JOIN leave_types lt ON lr.leave_type_id = lt.id
                JOIN users u ON lr.user_id = u.id
                JOIN departments d ON u.department_id = d.id
                WHERE lr.status = 'pending'
                ORDER BY lr.created_at DESC
                LIMIT 10
            ''')

        # Get pending coverage requests
        if user['role'] == 'manager':
            # Managers see requests from their department
            pending_coverage_requests = query_db('''
                SELECT cr.*, u.first_name, u.last_name, u.username
                FROM coverage_requests cr
                JOIN users u ON cr.user_id = u.id
                WHERE cr.status = 'pending' AND u.department_id = ?
                ORDER BY cr.created_at DESC
                LIMIT 10
            ''', [user['department_id']])
        else:
            # Admin, HR, and GM see all pending requests
            pending_coverage_requests = query_db('''
                SELECT cr.*, u.first_name, u.last_name, u.username, d.name as department_name
                FROM coverage_requests cr
                JOIN users u ON cr.user_id = u.id
                JOIN departments d ON u.department_id = d.id
                WHERE cr.status = 'pending'
                ORDER BY cr.created_at DESC
                LIMIT 10
            ''')

        # Get pending shift swap requests
        if user['role'] == 'manager':
            # Managers see requests from their department
            pending_shift_swaps = query_db('''
                SELECT ssr.*, u1.first_name as requester_first_name, u1.last_name as requester_last_name,
                       u2.first_name as target_first_name, u2.last_name as target_last_name
                FROM shift_swap_requests ssr
                JOIN users u1 ON ssr.user_id = u1.id
                JOIN users u2 ON ssr.swap_with_user_id = u2.id
                WHERE ssr.status = 'pending' AND u1.department_id = ?
                ORDER BY ssr.created_at DESC
                LIMIT 10
            ''', [user['department_id']])
        else:
            # Admin, HR, and GM see all pending requests
            pending_shift_swaps = query_db('''
                SELECT ssr.*, u1.first_name as requester_first_name, u1.last_name as requester_last_name,
                       u2.first_name as target_first_name, u2.last_name as target_last_name,
                       d.name as department_name
                FROM shift_swap_requests ssr
                JOIN users u1 ON ssr.user_id = u1.id
                JOIN users u2 ON ssr.swap_with_user_id = u2.id
                JOIN departments d ON u1.department_id = d.id
                WHERE ssr.status = 'pending'
                ORDER BY ssr.created_at DESC
                LIMIT 10
            ''')

        # Get employee statistics
        if user['role'] == 'manager':
            # Managers see employees in their department
            employee_count = query_db('''
                SELECT COUNT(*) as count
                FROM users
                WHERE role = 'employee' AND is_active = 1 AND department_id = ?
            ''', [user['department_id']], one=True)['count']

            on_leave_count = query_db('''
                SELECT COUNT(DISTINCT u.id) as count
                FROM users u
                JOIN leave_requests lr ON u.id = lr.user_id
                WHERE lr.status = 'approved'
                AND ? BETWEEN lr.start_date AND lr.end_date
                AND u.department_id = ?
            ''', [datetime.now().strftime('%Y-%m-%d'), user['department_id']], one=True)['count']
        else:
            # Admin, HR, and GM see all employees
            employee_count = query_db('''
                SELECT COUNT(*) as count
                FROM users
                WHERE role = 'employee' AND is_active = 1
            ''', one=True)['count']

            on_leave_count = query_db('''
                SELECT COUNT(DISTINCT u.id) as count
                FROM users u
                JOIN leave_requests lr ON u.id = lr.user_id
                WHERE lr.status = 'approved'
                AND ? BETWEEN lr.start_date AND lr.end_date
            ''', [datetime.now().strftime('%Y-%m-%d')], one=True)['count']

        return render_template('dashboard_supervisor.html', user=user,
                              pending_leave_requests=pending_leave_requests,
                              pending_coverage_requests=pending_coverage_requests,
                              pending_shift_swaps=pending_shift_swaps,
                              employee_count=employee_count,
                              on_leave_count=on_leave_count)

@app.route('/profile')
def profile():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى الملف الشخصي', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get pending profile change requests
    pending_requests = query_db('''
        SELECT * FROM profile_change_requests
        WHERE user_id = ? AND status = 'pending'
        ORDER BY created_at DESC
    ''', [user['id']])

    return render_template('profile.html', user=user, pending_requests=pending_requests)

@app.route('/profile/edit', methods=['POST'])
def edit_profile():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتعديل الملف الشخصي', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    request_type = request.form.get('request_type')
    reason = request.form.get('reason')

    db = get_db()

    if request_type == 'personal_info':
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')

        # Check if values are different from current values
        if first_name != user['first_name'] or last_name != user['last_name']:
            current_value = f"{user['first_name']} {user['last_name']}"
            new_value = f"{first_name} {last_name}"

            db.execute('''
                INSERT INTO profile_change_requests
                (user_id, request_type, current_value, new_value, reason, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', [user['id'], 'personal_info', current_value, new_value, reason, datetime.now()])
            db.commit()

            flash('تم إرسال طلب تعديل المعلومات الشخصية بنجاح. سيتم مراجعته من قبل مدير المختبر.', 'success')
        else:
            flash('لم يتم إجراء أي تغييرات على المعلومات الشخصية', 'info')

    elif request_type == 'account_info':
        username = request.form.get('username')
        email = request.form.get('email')

        # Check if username is different from current username
        if username != user['username']:
            # Check if username already exists
            existing_user = query_db('SELECT * FROM users WHERE username = ? AND id != ?', [username, user['id']], one=True)
            if existing_user:
                flash('اسم المستخدم موجود بالفعل', 'danger')
                return redirect(url_for('profile'))

            db.execute('''
                INSERT INTO profile_change_requests
                (user_id, request_type, current_value, new_value, reason, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', [user['id'], 'username', user['username'], username, reason, datetime.now()])
            db.commit()

            flash('تم إرسال طلب تغيير اسم المستخدم بنجاح. سيتم مراجعته من قبل مدير المختبر.', 'success')

        # Check if email is different from current email
        if email != user['email']:
            # Check if email already exists
            existing_email = query_db('SELECT * FROM users WHERE email = ? AND id != ?', [email, user['id']], one=True)
            if existing_email:
                flash('البريد الإلكتروني موجود بالفعل', 'danger')
                return redirect(url_for('profile'))

            db.execute('''
                INSERT INTO profile_change_requests
                (user_id, request_type, current_value, new_value, reason, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', [user['id'], 'email', user['email'], email, reason, datetime.now()])
            db.commit()

            flash('تم إرسال طلب تغيير البريد الإلكتروني بنجاح. سيتم مراجعته من قبل مدير المختبر.', 'success')

    return redirect(url_for('profile'))

@app.route('/profile/change_password', methods=['POST'])
def change_password_request():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتغيير كلمة المرور', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')
    reason = request.form.get('reason')

    # Verify current password
    if not check_password(user['password_hash'], current_password):
        flash('كلمة المرور الحالية غير صحيحة', 'danger')
        return redirect(url_for('profile'))

    # Check if new password matches confirmation
    if new_password != confirm_password:
        flash('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'danger')
        return redirect(url_for('profile'))

    # Create password change request
    db = get_db()
    db.execute('''
        INSERT INTO profile_change_requests
        (user_id, request_type, new_value, reason, created_at)
        VALUES (?, ?, ?, ?, ?)
    ''', [user['id'], 'password', hash_password(new_password), reason, datetime.now()])
    db.commit()

    flash('تم إرسال طلب تغيير كلمة المرور بنجاح. سيتم مراجعته من قبل مدير المختبر.', 'success')
    return redirect(url_for('profile'))

@app.route('/profile/requests')
def profile_requests():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض طلبات تعديل الملفات الشخصية', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view profile requests
    if user['role'] not in ['manager', 'admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get pending profile change requests
    pending_requests = query_db('''
        SELECT pcr.*, u.first_name, u.last_name, u.username
        FROM profile_change_requests pcr
        JOIN users u ON pcr.user_id = u.id
        WHERE pcr.status = 'pending'
        ORDER BY pcr.created_at DESC
    ''')

    # Get processed profile change requests
    processed_requests = query_db('''
        SELECT pcr.*, u.first_name, u.last_name, u.username
        FROM profile_change_requests pcr
        JOIN users u ON pcr.user_id = u.id
        WHERE pcr.status != 'pending'
        ORDER BY pcr.created_at DESC
        LIMIT 50
    ''')

    return render_template('profile_requests.html', user=user, pending_requests=pending_requests, processed_requests=processed_requests)

@app.route('/profile/requests/approve/<int:request_id>', methods=['POST'])
def approve_profile_request(request_id):
    if not is_authenticated():
        return jsonify({'success': False, 'error': 'يجب تسجيل الدخول للموافقة على طلبات تعديل الملفات الشخصية'})

    user = get_current_user()

    # Check if user has permission to approve profile requests
    if user['role'] not in ['manager', 'admin', 'hr', 'gm']:
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية للموافقة على طلبات تعديل الملفات الشخصية'})

    # Get profile change request
    profile_request = query_db('SELECT * FROM profile_change_requests WHERE id = ?', [request_id], one=True)
    if not profile_request:
        return jsonify({'success': False, 'error': 'طلب التعديل غير موجود'})

    # Get user who made the request
    request_user = query_db('SELECT * FROM users WHERE id = ?', [profile_request['user_id']], one=True)
    if not request_user:
        return jsonify({'success': False, 'error': 'المستخدم غير موجود'})

    db = get_db()

    # Update user based on request type
    if profile_request['request_type'] == 'username':
        # Check if username is still available
        existing_user = query_db('SELECT * FROM users WHERE username = ? AND id != ?',
                               [profile_request['new_value'], profile_request['user_id']], one=True)
        if existing_user:
            return jsonify({'success': False, 'error': 'اسم المستخدم أصبح مستخدماً بالفعل'})

        db.execute('UPDATE users SET username = ? WHERE id = ?',
                  [profile_request['new_value'], profile_request['user_id']])

    elif profile_request['request_type'] == 'email':
        # Check if email is still available
        existing_email = query_db('SELECT * FROM users WHERE email = ? AND id != ?',
                                [profile_request['new_value'], profile_request['user_id']], one=True)
        if existing_email:
            return jsonify({'success': False, 'error': 'البريد الإلكتروني أصبح مستخدماً بالفعل'})

        db.execute('UPDATE users SET email = ? WHERE id = ?',
                  [profile_request['new_value'], profile_request['user_id']])

    elif profile_request['request_type'] == 'password':
        db.execute('UPDATE users SET password_hash = ? WHERE id = ?',
                  [profile_request['new_value'], profile_request['user_id']])

    elif profile_request['request_type'] == 'personal_info':
        # Parse first_name and last_name from new_value
        names = profile_request['new_value'].split(' ', 1)
        first_name = names[0]
        last_name = names[1] if len(names) > 1 else ''

        db.execute('UPDATE users SET first_name = ?, last_name = ? WHERE id = ?',
                  [first_name, last_name, profile_request['user_id']])

    # Update request status
    db.execute('UPDATE profile_change_requests SET status = "approved", manager_approval = 1 WHERE id = ?', [request_id])

    # Add audit log
    db.execute('''
        INSERT INTO audit_logs
        (user_id, action, entity_type, entity_id, details, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', [user['id'], 'approve_profile_change', 'profile_change_request', request_id,
          f"تمت الموافقة على طلب تعديل {profile_request['request_type']} للمستخدم {request_user['username']}",
          datetime.now()])

    db.commit()

    return jsonify({'success': True})

@app.route('/profile/requests/reject/<int:request_id>', methods=['POST'])
def reject_profile_request(request_id):
    if not is_authenticated():
        return jsonify({'success': False, 'error': 'يجب تسجيل الدخول لرفض طلبات تعديل الملفات الشخصية'})

    user = get_current_user()

    # Check if user has permission to reject profile requests
    if user['role'] not in ['manager', 'admin', 'hr', 'gm']:
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية لرفض طلبات تعديل الملفات الشخصية'})

    # Get profile change request
    profile_request = query_db('SELECT * FROM profile_change_requests WHERE id = ?', [request_id], one=True)
    if not profile_request:
        return jsonify({'success': False, 'error': 'طلب التعديل غير موجود'})

    # Get user who made the request
    request_user = query_db('SELECT * FROM users WHERE id = ?', [profile_request['user_id']], one=True)
    if not request_user:
        return jsonify({'success': False, 'error': 'المستخدم غير موجود'})

    # Update request status
    db = get_db()
    db.execute('UPDATE profile_change_requests SET status = "rejected", manager_approval = -1 WHERE id = ?', [request_id])

    # Add audit log
    db.execute('''
        INSERT INTO audit_logs
        (user_id, action, entity_type, entity_id, details, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', [user['id'], 'reject_profile_change', 'profile_change_request', request_id,
          f"تم رفض طلب تعديل {profile_request['request_type']} للمستخدم {request_user['username']}",
          datetime.now()])

    db.commit()

    return jsonify({'success': True})

@app.route('/preferences')
def preferences():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى الإعدادات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    return render_template('preferences.html', user=user)

@app.route('/help_support')
def help_support():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى صفحة المساعدة والدعم', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    return render_template('help_support.html', user=user)



@app.route('/interactive_reports')
def interactive_reports():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى التقارير التفاعلية', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view reports
    if user['role'] not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get departments for filter
    departments = query_db('SELECT * FROM departments')

    return render_template('interactive_reports.html', user=user, departments=departments)

@app.route('/advanced_reports')
def advanced_reports():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى التقارير المتقدمة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view reports
    if user['role'] not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get departments for filter
    departments = query_db('SELECT * FROM departments')

    return render_template('advanced_reports.html', user=user, departments=departments)

@app.route('/reports')
def reports():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض التقارير', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view reports
    if user['role'] not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get departments for filtering
    departments = query_db('SELECT * FROM departments ORDER BY name')

    # Get leave types for filtering
    leave_types = query_db('SELECT * FROM leave_types ORDER BY name')

    # Get leave statistics by department
    dept_stats = query_db('''
        SELECT d.name as department_name, COUNT(lr.id) as leave_count
        FROM departments d
        LEFT JOIN users u ON u.department_id = d.id
        LEFT JOIN leave_requests lr ON lr.user_id = u.id
        WHERE lr.status = 'approved'
        GROUP BY d.id
        ORDER BY leave_count DESC
    ''')

    # Get leave statistics by type
    type_stats = query_db('''
        SELECT lt.name as leave_type_name, COUNT(lr.id) as leave_count
        FROM leave_types lt
        LEFT JOIN leave_requests lr ON lr.leave_type_id = lt.id
        WHERE lr.status = 'approved'
        GROUP BY lt.id
        ORDER BY leave_count DESC
    ''')

    # Get leave statistics by month
    month_stats = query_db('''
        SELECT strftime('%m', start_date) as month, COUNT(id) as leave_count
        FROM leave_requests
        WHERE status = 'approved' AND strftime('%Y', start_date) = ?
        GROUP BY month
        ORDER BY month
    ''', [str(datetime.now().year)])

    # Convert month numbers to names
    month_names = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']

    # Convert sqlite3.Row objects to dictionaries and add month_name
    month_stats_with_names = []
    for stat in month_stats:
        stat_dict = dict(stat)
        stat_dict['month_name'] = month_names[int(stat['month']) - 1]
        month_stats_with_names.append(stat_dict)

    month_stats = month_stats_with_names

    return render_template('reports.html', user=user, departments=departments, leave_types=leave_types,
                          dept_stats=dept_stats, type_stats=type_stats, month_stats=month_stats)

@app.route('/request_reports')
def request_reports():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض تقارير الطلبات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get current month and year, or use query parameters
    current_month = int(request.args.get('month', datetime.now().month))
    current_year = int(request.args.get('year', datetime.now().year))

    # Calculate days in month
    days_in_month = calendar.monthrange(current_year, current_month)[1]

    # Get leave statistics
    leave_stats = {
        'total': query_db('SELECT COUNT(*) as count FROM leave_requests', one=True)['count'],
        'approved': query_db('SELECT COUNT(*) as count FROM leave_requests WHERE status = "approved"', one=True)['count'],
        'rejected': query_db('SELECT COUNT(*) as count FROM leave_requests WHERE status = "rejected"', one=True)['count'],
        'pending': query_db('SELECT COUNT(*) as count FROM leave_requests WHERE status = "pending"', one=True)['count']
    }

    # Get coverage statistics
    coverage_stats = {
        'total': query_db('SELECT COUNT(*) as count FROM coverage_requests', one=True)['count'],
        'approved': query_db('SELECT COUNT(*) as count FROM coverage_requests WHERE status = "approved"', one=True)['count'],
        'rejected': query_db('SELECT COUNT(*) as count FROM coverage_requests WHERE status = "rejected"', one=True)['count'],
        'pending': query_db('SELECT COUNT(*) as count FROM coverage_requests WHERE status = "pending"', one=True)['count']
    }

    # Get shift swap statistics
    swap_stats = {
        'total': query_db('SELECT COUNT(*) as count FROM shift_swap_requests', one=True)['count'],
        'approved': query_db('SELECT COUNT(*) as count FROM shift_swap_requests WHERE status = "approved"', one=True)['count'],
        'rejected': query_db('SELECT COUNT(*) as count FROM shift_swap_requests WHERE status = "rejected"', one=True)['count'],
        'pending': query_db('SELECT COUNT(*) as count FROM shift_swap_requests WHERE status = "pending"', one=True)['count']
    }

    # Get monthly data for charts
    monthly_leave_data = [0] * 12
    monthly_coverage_data = [0] * 12
    monthly_swap_data = [0] * 12

    # Get leave data by month
    leave_month_data = query_db('''
        SELECT strftime('%m', start_date) as month, COUNT(*) as count
        FROM leave_requests
        WHERE strftime('%Y', start_date) = ?
        GROUP BY month
    ''', [str(current_year)])

    for data in leave_month_data:
        monthly_leave_data[int(data['month']) - 1] = data['count']

    # Get coverage data by month
    coverage_month_data = query_db('''
        SELECT strftime('%m', coverage_date) as month, COUNT(*) as count
        FROM coverage_requests
        WHERE strftime('%Y', coverage_date) = ?
        GROUP BY month
    ''', [str(current_year)])

    for data in coverage_month_data:
        monthly_coverage_data[int(data['month']) - 1] = data['count']

    # Get swap data by month
    try:
        # Try to get data for one_day swaps using swap_date
        swap_month_data_one_day = query_db('''
            SELECT strftime('%m', swap_date) as month, COUNT(*) as count
            FROM shift_swap_requests
            WHERE strftime('%Y', swap_date) = ? AND swap_duration = 'one_day'
            GROUP BY month
        ''', [str(current_year)])
    except:
        # If swap_date column doesn't exist, use an empty list
        swap_month_data_one_day = []

    try:
        # Try to get data for multiple_days swaps using start_date
        swap_month_data_multiple_days = query_db('''
            SELECT strftime('%m', start_date) as month, COUNT(*) as count
            FROM shift_swap_requests
            WHERE strftime('%Y', start_date) = ? AND swap_duration = 'multiple_days'
            GROUP BY month
        ''', [str(current_year)])
    except:
        # If start_date column doesn't exist, use an empty list
        swap_month_data_multiple_days = []

    for data in swap_month_data_one_day:
        monthly_swap_data[int(data['month']) - 1] += data['count']

    for data in swap_month_data_multiple_days:
        monthly_swap_data[int(data['month']) - 1] += data['count']

    # Get all employees
    employees = query_db('''
        SELECT id, first_name, last_name
        FROM users
        WHERE role = 'employee' AND is_active = 1
        ORDER BY first_name, last_name
    ''')

    # Function to get shift for a specific employee and day
    def get_shift(employee_id, day):
        # Query the shift_assignments table for the specific employee and day
        shift_assignment = query_db('''
            SELECT sa.shift_type
            FROM shift_assignments sa
            JOIN shift_schedules ss ON sa.schedule_id = ss.id
            WHERE sa.user_id = ? AND sa.day = ? AND ss.month = ? AND ss.year = ?
            ORDER BY ss.created_at DESC
            LIMIT 1
        ''', [employee_id, day, current_month, current_year], one=True)

        if shift_assignment:
            return shift_assignment['shift_type']

        # If no assignment found, return None
        return None

    return render_template('request_reports.html',
                          user=user,
                          current_month=current_month,
                          current_year=current_year,
                          days_in_month=days_in_month,
                          leave_stats=leave_stats,
                          coverage_stats=coverage_stats,
                          swap_stats=swap_stats,
                          monthly_leave_data=monthly_leave_data,
                          monthly_coverage_data=monthly_coverage_data,
                          monthly_swap_data=monthly_swap_data,
                          employees=employees,
                          get_shift=get_shift)













@app.route('/leave/new', methods=['GET', 'POST'])
def new_leave():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لطلب إجازة جديدة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get leave types
    leave_types = query_db('SELECT * FROM leave_types')

    # Get coverage days for the user (for coverage compensation leave type)
    coverage_days = query_db('''
        SELECT cd.*,
               CASE
                   WHEN cd.shift_type = 'morning' THEN 'صباحي'
                   WHEN cd.shift_type = 'evening' THEN 'مسائي'
                   WHEN cd.shift_type = 'night' THEN 'ليلي'
                   ELSE cd.shift_type
               END as shift_type_ar
        FROM coverage_days cd
        WHERE cd.user_id = ? AND cd.status = 'approved'
        ORDER BY cd.coverage_date DESC
    ''', [user['id']])

    if request.method == 'POST':
        leave_type_id = request.form.get('leave_type_id')
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        reason = request.form.get('reason')
        coverage_day_ids = request.form.getlist('coverage_day_ids')

        # Handle file upload
        document_filename = None
        if 'document' in request.files:
            file = request.files['document']
            if file and file.filename != '' and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                # Add timestamp to filename to avoid conflicts
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                document_filename = timestamp + filename
                file.save(os.path.join(app.config['UPLOAD_FOLDER'], document_filename))

        # Get leave type name
        leave_type = query_db('SELECT * FROM leave_types WHERE id = ?', [leave_type_id], one=True)

        # Check if this is a coverage compensation leave
        is_coverage_compensation_leave = leave_type and leave_type['name'] == 'بدل يوم تغطية'

        if is_coverage_compensation_leave and not coverage_day_ids:
            flash('يجب اختيار يوم تغطية واحد على الأقل', 'danger')
            return render_template('new_leave.html', user=user, leave_types=leave_types, coverage_days=coverage_days)

        # Validate dates
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

            if start_date_obj > end_date_obj:
                flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'danger')
                return render_template('new_leave.html', user=user, leave_types=leave_types, coverage_days=coverage_days)

            if start_date_obj < datetime.now().date():
                flash('لا يمكن طلب إجازة في تاريخ سابق', 'danger')
                return render_template('new_leave.html', user=user, leave_types=leave_types, coverage_days=coverage_days)
        except ValueError:
            flash('صيغة التاريخ غير صحيحة', 'danger')
            return render_template('new_leave.html', user=user, leave_types=leave_types, coverage_days=coverage_days)

        # Calculate days
        days = (end_date_obj - start_date_obj).days + 1

        db = get_db()

        # For coverage compensation leave, we need to check coverage days and balance
        if is_coverage_compensation_leave:
            # Validate all selected coverage days
            valid_coverage_days = []
            for coverage_day_id in coverage_day_ids:
                coverage_day = query_db('SELECT * FROM coverage_days WHERE id = ?', [coverage_day_id], one=True)
                if not coverage_day or coverage_day['user_id'] != user['id']:
                    flash(f'يوم التغطية رقم {coverage_day_id} غير موجود أو ليس لديك صلاحية لاستخدامه', 'danger')
                    return render_template('new_leave.html', user=user, leave_types=leave_types, coverage_days=coverage_days)
                if coverage_day['status'] == 'used':
                    flash(f'يوم التغطية بتاريخ {coverage_day["coverage_date"]} تم استخدامه من قبل', 'danger')
                    return render_template('new_leave.html', user=user, leave_types=leave_types, coverage_days=coverage_days)
                valid_coverage_days.append(coverage_day)

            # Ensure leave balance exists
            ensure_leave_balances(user['id'])

            # Check coverage compensation balance
            leave_balance = query_db('''
                SELECT * FROM leave_balances
                WHERE user_id = ? AND leave_type_id = ? AND year = ?
            ''', [user['id'], leave_type_id, datetime.now().year], one=True)

            # Number of coverage days selected should match the leave days requested
            if len(valid_coverage_days) != days:
                flash(f'عدد أيام التغطية المختارة ({len(valid_coverage_days)}) لا يتطابق مع عدد أيام الإجازة المطلوبة ({days})', 'danger')
                return render_template('new_leave.html', user=user, leave_types=leave_types, coverage_days=coverage_days)

            if not leave_balance or leave_balance['remaining_days'] < days:
                flash(f'رصيد بدل التغطية غير كافي. الرصيد المتبقي: {leave_balance["remaining_days"] if leave_balance else 0} يوم', 'danger')
                return render_template('new_leave.html', user=user, leave_types=leave_types, coverage_days=coverage_days)
        else:
            # Ensure leave balance exists
            ensure_leave_balances(user['id'])

            # Check leave balance for non-coverage leave types
            leave_balance = query_db('''
                SELECT * FROM leave_balances
                WHERE user_id = ? AND leave_type_id = ? AND year = ?
            ''', [user['id'], leave_type_id, datetime.now().year], one=True)

            if not leave_balance:
                # This should not happen since we called ensure_leave_balances, but just in case
                flash('حدث خطأ في رصيد الإجازة. يرجى الاتصال بالمسؤول.', 'danger')
                return render_template('new_leave.html', user=user, leave_types=leave_types, coverage_days=coverage_days)

            if leave_balance['remaining_days'] < days:
                flash(f'رصيد الإجازة غير كافي. الرصيد المتبقي: {leave_balance["remaining_days"]} يوم', 'danger')
                return render_template('new_leave.html', user=user, leave_types=leave_types, coverage_days=coverage_days)

        # Insert leave request
        db.execute('''
            INSERT INTO leave_requests
            (user_id, leave_type_id, start_date, end_date, reason, status, document_path, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', [user['id'], leave_type_id, start_date, end_date, reason, 'pending', document_filename, datetime.now()])

        # If it's a coverage compensation leave, it still needs approval
        if is_coverage_compensation_leave:
            # Get the last inserted leave request
            leave_request = query_db('SELECT last_insert_rowid() as id', one=True)
            leave_id = leave_request['id']

            # Store coverage day IDs for later use when request is approved
            coverage_day_ids_str = ','.join(coverage_day_ids)
            db.execute('''
                UPDATE leave_requests
                SET coverage_day_ids = ?
                WHERE id = ?
            ''', [coverage_day_ids_str, leave_id])

            # Add audit log
            db.execute('''
                INSERT INTO audit_logs
                (user_id, action, entity_type, entity_id, details, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', [user['id'], 'create', 'leave_request', leave_id, f'طلب إجازة بدل {len(coverage_day_ids)} أيام تغطية', datetime.now()])

        db.commit()

        flash('تم تقديم طلب الإجازة بنجاح', 'success')
        return redirect(url_for('my_leaves'))

    return render_template('new_leave.html', user=user, leave_types=leave_types, coverage_days=coverage_days)

@app.route('/leaves')
def my_leaves():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض الإجازات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get all leave requests for the user
    leave_requests = query_db('''
        SELECT lr.*, lt.name as leave_type_name
        FROM leave_requests lr
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        WHERE lr.user_id = ?
        ORDER BY lr.created_at DESC
    ''', [user['id']])

    return render_template('my_leaves.html', user=user, leave_requests=leave_requests)

@app.route('/leave/requests')
def leave_requests():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض طلبات الإجازات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view leave requests
    if user['role'] not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get leave requests based on role
    if user['role'] == 'manager':
        # Managers can see requests from their department
        leave_requests = query_db('''
            SELECT lr.*, lt.name as leave_type_name, u.first_name, u.last_name, u.username
            FROM leave_requests lr
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            JOIN users u ON lr.user_id = u.id
            WHERE u.department_id = ?
            ORDER BY lr.created_at DESC
        ''', [user['department_id']])
    else:
        # Admin, HR, and GM can see all requests
        leave_requests = query_db('''
            SELECT lr.*, lt.name as leave_type_name, u.first_name, u.last_name, u.username, d.name as department_name
            FROM leave_requests lr
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            JOIN users u ON lr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            ORDER BY lr.created_at DESC
        ''')

    return render_template('leave_requests.html', user=user, leave_requests=leave_requests)

@app.route('/leave/approve/<int:leave_id>/<string:role>', methods=['GET', 'POST'])
def approve_leave(leave_id, role):
    if not is_authenticated():
        flash('يجب تسجيل الدخول للموافقة على الإجازات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to approve leave
    allowed_roles = {
        'manager': ['manager'],
        'hr': ['hr'],
        'hospital_manager': ['hospital_manager', 'admin']
    }

    if role not in allowed_roles or user['role'] not in allowed_roles[role]:
        flash('ليس لديك صلاحية للموافقة على الإجازة', 'danger')
        return redirect(url_for('leave_requests'))

    # Get leave request
    leave_request = query_db('SELECT * FROM leave_requests WHERE id = ?', [leave_id], one=True)

    if not leave_request:
        flash('طلب الإجازة غير موجود', 'danger')
        return redirect(url_for('leave_requests'))

    if request.method == 'POST':
        comment = request.form.get('comment', '')

        # Update leave request
        db = get_db()

        if role == 'manager':
            db.execute('UPDATE leave_requests SET manager_approval = 1, manager_comment = ? WHERE id = ?', [comment, leave_id])
        elif role == 'hr':
            db.execute('UPDATE leave_requests SET hr_approval = 1, hr_comment = ? WHERE id = ?', [comment, leave_id])
        elif role == 'hospital_manager':
            db.execute('UPDATE leave_requests SET hospital_manager_approval = 1, hospital_manager_comment = ? WHERE id = ?', [comment, leave_id])

        # Check approval hierarchy: manager -> hr -> hospital_manager
        leave_request = query_db('SELECT * FROM leave_requests WHERE id = ?', [leave_id], one=True)

        if (leave_request['manager_approval'] == 1 and
            leave_request['hr_approval'] == 1 and
            leave_request['hospital_manager_approval'] == 1):
            db.execute('UPDATE leave_requests SET status = "approved" WHERE id = ?', [leave_id])

            # Update leave balance only when fully approved
            leave_days = (datetime.strptime(leave_request['end_date'], '%Y-%m-%d') -
                          datetime.strptime(leave_request['start_date'], '%Y-%m-%d')).days + 1

        # Ensure leave balance exists
        ensure_leave_balances(leave_request['user_id'])

        # Check if it's a coverage compensation leave
        leave_type = query_db('SELECT * FROM leave_types WHERE id = ?', [leave_request['leave_type_id']], one=True)

        if leave_type and leave_type['name'] == 'بدل يوم تغطية':
            # For coverage compensation leave, deduct from coverage compensation balance
            use_coverage_compensation_days(leave_request['user_id'], leave_days)

            # Mark coverage days as used
            if leave_request['coverage_day_ids']:
                coverage_day_ids = leave_request['coverage_day_ids'].split(',')
                for coverage_day_id in coverage_day_ids:
                    if coverage_day_id.strip():
                        db.execute('UPDATE coverage_days SET status = "used" WHERE id = ?', [coverage_day_id.strip()])
        else:
            # For regular leave types, deduct from normal balance
            # Get current balance
            balance = query_db('''
                SELECT * FROM leave_balances
                WHERE user_id = ? AND leave_type_id = ? AND year = ?
            ''', [leave_request['user_id'], leave_request['leave_type_id'], datetime.now().year], one=True)

            if balance:
                # Update existing balance
                db.execute('''
                    UPDATE leave_balances
                    SET used_days = used_days + ?, remaining_days = remaining_days - ?
                    WHERE user_id = ? AND leave_type_id = ? AND year = ?
                ''', [leave_days, leave_days, leave_request['user_id'], leave_request['leave_type_id'], datetime.now().year])
            else:
                # This should not happen since we called ensure_leave_balances, but just in case
                leave_type_info = query_db('SELECT * FROM leave_types WHERE id = ?', [leave_request['leave_type_id']], one=True)
                if leave_type_info:
                    db.execute('''
                        INSERT INTO leave_balances
                        (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', [leave_request['user_id'], leave_request['leave_type_id'], datetime.now().year,
                          leave_type_info['default_days'], leave_days, leave_type_info['default_days'] - leave_days])

        db.commit()

        flash('تمت الموافقة على الإجازة بنجاح', 'success')
        return redirect(url_for('leave_requests'))

    # If GET request, show approval form
    return render_template('approve_leave.html', user=user, leave_request=leave_request, role=role)

@app.route('/leave/reject/<int:leave_id>/<string:role>', methods=['GET', 'POST'])
def reject_leave(leave_id, role):
    if not is_authenticated():
        flash('يجب تسجيل الدخول لرفض الإجازات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to reject leave
    allowed_roles = {
        'manager': ['manager'],
        'hr': ['hr'],
        'hospital_manager': ['hospital_manager', 'admin']
    }

    if role not in allowed_roles or user['role'] not in allowed_roles[role]:
        flash('ليس لديك صلاحية لرفض الإجازة', 'danger')
        return redirect(url_for('leave_requests'))

    # Get leave request
    leave_request = query_db('SELECT * FROM leave_requests WHERE id = ?', [leave_id], one=True)

    if not leave_request:
        flash('طلب الإجازة غير موجود', 'danger')
        return redirect(url_for('leave_requests'))

    if request.method == 'POST':
        comment = request.form.get('comment', '')

        # Update leave request
        db = get_db()

        if role == 'manager':
            db.execute('UPDATE leave_requests SET status = "rejected", manager_comment = ? WHERE id = ?', [comment, leave_id])
        elif role == 'hr':
            db.execute('UPDATE leave_requests SET status = "rejected", hr_comment = ? WHERE id = ?', [comment, leave_id])
        elif role == 'hospital_manager':
            db.execute('UPDATE leave_requests SET status = "rejected", hospital_manager_comment = ? WHERE id = ?', [comment, leave_id])

        db.commit()

        flash('تم رفض الإجازة بنجاح', 'success')
        return redirect(url_for('leave_requests'))

    # If GET request, show rejection form
    return render_template('reject_leave.html', user=user, leave_request=leave_request, role=role)

@app.route('/coverage_requests')
def coverage_requests():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض طلبات التغطية', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get coverage requests based on role
    if user['role'] == 'manager':
        # Managers can see requests from their department
        coverage_requests = query_db('''
            SELECT cr.*, u.first_name, u.last_name, u.username
            FROM coverage_requests cr
            JOIN users u ON cr.user_id = u.id
            WHERE u.department_id = ?
            ORDER BY cr.created_at DESC
        ''', [user['department_id']])
    elif user['role'] in ['admin', 'hr', 'gm']:
        # Admin, HR, and GM can see all requests
        coverage_requests = query_db('''
            SELECT cr.*, u.first_name, u.last_name, u.username, d.name as department_name
            FROM coverage_requests cr
            JOIN users u ON cr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            ORDER BY cr.created_at DESC
        ''')
    else:
        # Regular employees can see only their requests
        coverage_requests = query_db('''
            SELECT cr.*
            FROM coverage_requests cr
            WHERE cr.user_id = ?
            ORDER BY cr.created_at DESC
        ''', [user['id']])

    return render_template('coverage_requests.html', user=user, coverage_requests=coverage_requests)

@app.route('/new_coverage', methods=['GET', 'POST'])
def new_coverage():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لطلب تغطية جديدة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    if request.method == 'POST':
        coverage_date = request.form.get('coverage_date')
        coverage_type = request.form.get('coverage_type')
        coverage_reason = request.form.get('coverage_reason')
        description = request.form.get('description')

        # Validate date
        try:
            coverage_date_obj = datetime.strptime(coverage_date, '%Y-%m-%d').date()
            if coverage_date_obj < datetime.now().date():
                flash('لا يمكن طلب تغطية في تاريخ سابق', 'danger')
                return render_template('new_coverage.html', user=user)
        except ValueError:
            flash('صيغة التاريخ غير صحيحة', 'danger')
            return render_template('new_coverage.html', user=user)

        # Check if it's a Friday (day 4 in Python's datetime, where Monday is 0)
        is_friday = coverage_date_obj.weekday() == 4

        # If it's a Friday, set status to pending and require manager approval only
        status = 'pending'

        # Insert coverage request
        db = get_db()
        db.execute('''
            INSERT INTO coverage_requests
            (user_id, coverage_date, coverage_type, coverage_reason, description, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', [user['id'], coverage_date, coverage_type, coverage_reason, description, status, datetime.now()])
        db.commit()

        flash('تم تقديم طلب التغطية بنجاح', 'success')
        if is_friday:
            flash('ملاحظة: طلب التغطية في يوم الجمعة يتطلب موافقة مدير المختبر فقط', 'info')

        return redirect(url_for('coverage_requests'))

    return render_template('new_coverage.html', user=user)

@app.route('/approve_coverage', methods=['POST'])
def approve_coverage():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للموافقة على التغطية', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    data = request.json
    coverage_id = data.get('coverage_id')
    approval_type = data.get('approval_type')
    action = data.get('action')

    # Check if user has permission to approve/reject
    if (approval_type == 'manager' and user['role'] != 'manager') or \
       (approval_type == 'hr' and user['role'] != 'hr'):
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية للموافقة/الرفض'})

    # Get coverage request
    coverage_request = query_db('SELECT * FROM coverage_requests WHERE id = ?', [coverage_id], one=True)
    if not coverage_request:
        return jsonify({'success': False, 'error': 'طلب التغطية غير موجود'})

    # Update coverage request
    db = get_db()
    if action == 'approve':
        if approval_type == 'manager':
            db.execute('UPDATE coverage_requests SET manager_approval = 1 WHERE id = ?', [coverage_id])
        elif approval_type == 'hr':
            db.execute('UPDATE coverage_requests SET hr_approval = 1 WHERE id = ?', [coverage_id])

        # Check if both approvals are done
        coverage_request = query_db('SELECT * FROM coverage_requests WHERE id = ?', [coverage_id], one=True)
        if coverage_request['manager_approval'] == 1 and coverage_request['hr_approval'] == 1:
            db.execute('UPDATE coverage_requests SET status = "approved" WHERE id = ?', [coverage_id])

            # Add to coverage days
            db.execute('''
                INSERT INTO coverage_days
                (user_id, coverage_date, shift_type, status, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', [coverage_request['user_id'], coverage_request['coverage_date'],
                  coverage_request['coverage_type'], 'approved', datetime.now()])

            # Add coverage compensation leave balance
            add_coverage_compensation_day(coverage_request['user_id'], coverage_request['coverage_date'], coverage_request['coverage_type'])
    else:  # reject
        if approval_type == 'manager':
            db.execute('UPDATE coverage_requests SET manager_approval = -1, status = "rejected" WHERE id = ?', [coverage_id])
        elif approval_type == 'hr':
            db.execute('UPDATE coverage_requests SET hr_approval = -1, status = "rejected" WHERE id = ?', [coverage_id])

    db.commit()
    return jsonify({'success': True})

@app.route('/cancel_coverage', methods=['POST'])
def cancel_coverage():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لإلغاء التغطية', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    data = request.json
    coverage_id = data.get('coverage_id')

    # Get coverage request
    coverage_request = query_db('SELECT * FROM coverage_requests WHERE id = ?', [coverage_id], one=True)
    if not coverage_request or coverage_request['user_id'] != user['id']:
        return jsonify({'success': False, 'error': 'طلب التغطية غير موجود أو ليس لديك صلاحية لإلغائه'})

    # Delete coverage request
    db = get_db()
    db.execute('DELETE FROM coverage_requests WHERE id = ?', [coverage_id])
    db.commit()

    return jsonify({'success': True})

@app.route('/my_coverage')
def my_coverage():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض تغطياتي', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get coverage days for the user
    coverage_days = query_db('''
        SELECT * FROM coverage_days
        WHERE user_id = ?
        ORDER BY coverage_date DESC
    ''', [user['id']])

    return render_template('my_coverage.html', user=user, coverage_days=coverage_days)

@app.route('/shift_swap_requests')
def shift_swap_requests():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض طلبات تبديل الدوام', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get shift swap requests based on role
    if user['role'] == 'manager':
        # Managers can see requests from their department
        swap_requests = query_db('''
            SELECT ssr.*, u.first_name, u.last_name, u.username,
                   u2.first_name as swap_with_first_name, u2.last_name as swap_with_last_name
            FROM shift_swap_requests ssr
            JOIN users u ON ssr.user_id = u.id
            LEFT JOIN users u2 ON ssr.swap_with_user_id = u2.id
            WHERE u.department_id = ?
            ORDER BY ssr.created_at DESC
        ''', [user['department_id']])
    elif user['role'] in ['admin', 'hr', 'gm']:
        # Admin, HR, and GM can see all requests
        swap_requests = query_db('''
            SELECT ssr.*, u.first_name, u.last_name, u.username, d.name as department_name,
                   u2.first_name as swap_with_first_name, u2.last_name as swap_with_last_name
            FROM shift_swap_requests ssr
            JOIN users u ON ssr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            LEFT JOIN users u2 ON ssr.swap_with_user_id = u2.id
            ORDER BY ssr.created_at DESC
        ''')
    else:
        # Regular employees can see only their requests
        swap_requests = query_db('''
            SELECT ssr.*, u2.first_name as swap_with_first_name, u2.last_name as swap_with_last_name
            FROM shift_swap_requests ssr
            LEFT JOIN users u2 ON ssr.swap_with_user_id = u2.id
            WHERE ssr.user_id = ?
            ORDER BY ssr.created_at DESC
        ''', [user['id']])

    return render_template('shift_swap_requests.html', user=user, swap_requests=swap_requests)

@app.route('/new_shift_swap', methods=['GET', 'POST'])
def new_shift_swap():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لطلب تبديل دوام جديد', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get all employees for swap selection
    employees = query_db('''
        SELECT id, first_name, last_name
        FROM users
        WHERE role = 'employee' AND is_active = 1
        ORDER BY first_name, last_name
    ''')

    # Get coverage days for the user
    coverage_days = query_db('''
        SELECT * FROM coverage_days
        WHERE user_id = ? AND status = 'approved' AND status != 'used'
        ORDER BY coverage_date DESC
    ''', [user['id']])

    if request.method == 'POST':
        swap_type = request.form.get('swap_type')
        swap_reason_type = request.form.get('swap_reason_type')
        reason = request.form.get('reason')
        shift_type = request.form.get('shift_type')
        requester_shift_type = request.form.get('requester_shift_type')

        # Get dates based on swap type
        swap_date = None
        start_date = None
        end_date = None
        swap_duration = None
        swap_with_user_id = None
        coverage_day_id = None

        if swap_type == 'use_coverage_day':
            # استغناء يوم تغطية
            coverage_day_id = request.form.get('coverage_day_id')

            # التحقق من وجود يوم التغطية
            coverage_day = query_db('SELECT * FROM coverage_days WHERE id = ?', [coverage_day_id], one=True)
            if not coverage_day or coverage_day['user_id'] != user['id']:
                flash('يوم التغطية غير موجود أو ليس لديك صلاحية لاستخدامه', 'danger')
                return render_template('new_shift_swap.html', user=user, employees=employees, coverage_days=coverage_days)

            if coverage_day['status'] == 'used':
                flash('تم استخدام يوم التغطية هذا من قبل', 'danger')
                return render_template('new_shift_swap.html', user=user, employees=employees, coverage_days=coverage_days)

            # استخدام تاريخ يوم التغطية كتاريخ للتبديل
            swap_date = coverage_day['coverage_date']
            swap_duration = 'one_day'

        else:
            # تبديل عادي مع أو بدون موظف
            swap_duration = request.form.get('swap_duration')
            if swap_type == 'with_employee':
                swap_with_user_id = request.form.get('swap_with_user_id')

            if swap_duration == 'one_day':
                swap_date = request.form.get('swap_date')
                # Validate date
                try:
                    swap_date_obj = datetime.strptime(swap_date, '%Y-%m-%d').date()
                    if swap_date_obj < datetime.now().date():
                        flash('لا يمكن طلب تبديل في تاريخ سابق', 'danger')
                        return render_template('new_shift_swap.html', user=user, employees=employees, coverage_days=coverage_days)
                except ValueError:
                    flash('صيغة التاريخ غير صحيحة', 'danger')
                    return render_template('new_shift_swap.html', user=user, employees=employees, coverage_days=coverage_days)
            elif swap_duration == 'multiple_days':
                start_date = request.form.get('start_date')
                end_date = request.form.get('end_date')
                # Validate dates
                try:
                    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

                    if start_date_obj < datetime.now().date() or end_date_obj < datetime.now().date():
                        flash('لا يمكن طلب تبديل في تاريخ سابق', 'danger')
                        return render_template('new_shift_swap.html', user=user, employees=employees, coverage_days=coverage_days)

                    if start_date_obj > end_date_obj:
                        flash('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'danger')
                        return render_template('new_shift_swap.html', user=user, employees=employees, coverage_days=coverage_days)
                except ValueError:
                    flash('صيغة التاريخ غير صحيحة', 'danger')
                    return render_template('new_shift_swap.html', user=user, employees=employees, coverage_days=coverage_days)

        # Check if it's a Friday (day 4 in Python's datetime, where Monday is 0)
        is_friday = False
        if swap_duration == 'one_day':
            is_friday = swap_date_obj.weekday() == 4
        elif swap_duration == 'multiple_days':
            # Check if any day in the range is a Friday
            current_date = start_date_obj
            while current_date <= end_date_obj:
                if current_date.weekday() == 4:
                    is_friday = True
                    break
                current_date += timedelta(days=1)

        # If it's a Friday and swap_reason_type is coverage_day, require manager approval only
        manager_approval = 0
        hr_approval = 0

        if is_friday and swap_reason_type == 'coverage_day':
            flash('ملاحظة: طلب التبديل في يوم الجمعة يتطلب موافقة مدير المختبر فقط', 'info')
            hr_approval = 1  # Auto-approve HR part

        # Insert shift swap request
        db = get_db()

        # إذا كان الطلب هو استغناء يوم تغطية
        if swap_type == 'use_coverage_day':
            # تحديث حالة يوم التغطية إلى "مستخدم"
            db.execute('UPDATE coverage_days SET status = "used" WHERE id = ?', [coverage_day_id])

            # إضافة طلب تبديل الدوام
            db.execute('''
                INSERT INTO shift_swap_requests
                (user_id, swap_type, swap_reason_type, swap_duration, swap_with_user_id,
                 swap_date, start_date, end_date, reason, status, manager_approval, hr_approval, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', [user['id'], swap_type, swap_reason_type, swap_duration, swap_with_user_id,
                  swap_date, start_date, end_date, reason, 'approved', 1, 1, datetime.now()])

            flash('تم استغناء يوم التغطية بنجاح', 'success')
        else:
            # إضافة طلب تبديل الدوام العادي
            shift_type_value = shift_type if swap_type != 'use_coverage_day' else None

            # إضافة الحقل الجديد للشفت الطالب
            requester_shift_type_value = requester_shift_type if swap_type != 'use_coverage_day' else None

            db.execute('''
                INSERT INTO shift_swap_requests
                (user_id, swap_type, swap_reason_type, swap_duration, swap_with_user_id,
                 swap_date, start_date, end_date, reason, status, manager_approval, hr_approval, created_at, shift_type, requester_shift_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', [user['id'], swap_type, swap_reason_type, swap_duration, swap_with_user_id,
                  swap_date, start_date, end_date, reason, 'pending', manager_approval, hr_approval, datetime.now(), shift_type_value, requester_shift_type_value])

            flash('تم تقديم طلب تبديل الدوام بنجاح', 'success')

        db.commit()
        return redirect(url_for('shift_swap_requests'))

    return render_template('new_shift_swap.html', user=user, employees=employees, coverage_days=coverage_days)

@app.route('/approve_shift_swap', methods=['POST'])
def approve_shift_swap():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للموافقة على تبديل الدوام', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    data = request.json
    swap_id = data.get('swap_id')
    approval_type = data.get('approval_type')
    action = data.get('action')

    # Check if user has permission to approve/reject
    if (approval_type == 'manager' and user['role'] != 'manager') or \
       (approval_type == 'hr' and user['role'] != 'hr'):
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية للموافقة/الرفض'})

    # Get shift swap request
    swap_request = query_db('SELECT * FROM shift_swap_requests WHERE id = ?', [swap_id], one=True)
    if not swap_request:
        return jsonify({'success': False, 'error': 'طلب تبديل الدوام غير موجود'})

    # Update shift swap request
    db = get_db()
    if action == 'approve':
        if approval_type == 'manager':
            db.execute('UPDATE shift_swap_requests SET manager_approval = 1 WHERE id = ?', [swap_id])
        elif approval_type == 'hr':
            db.execute('UPDATE shift_swap_requests SET hr_approval = 1 WHERE id = ?', [swap_id])

        # Check if both approvals are done
        swap_request = query_db('SELECT * FROM shift_swap_requests WHERE id = ?', [swap_id], one=True)
        if swap_request['manager_approval'] == 1 and swap_request['hr_approval'] == 1:
            db.execute('UPDATE shift_swap_requests SET status = "approved" WHERE id = ?', [swap_id])

            # Update shift assignments if they exist
            # This would be implemented when the shift schedule feature is added
    else:  # reject
        if approval_type == 'manager':
            db.execute('UPDATE shift_swap_requests SET manager_approval = -1, status = "rejected" WHERE id = ?', [swap_id])
        elif approval_type == 'hr':
            db.execute('UPDATE shift_swap_requests SET hr_approval = -1, status = "rejected" WHERE id = ?', [swap_id])

    db.commit()
    return jsonify({'success': True})

@app.route('/cancel_shift_swap', methods=['POST'])
def cancel_shift_swap():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لإلغاء تبديل الدوام', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    data = request.json
    swap_id = data.get('swap_id')

    # Get shift swap request
    swap_request = query_db('SELECT * FROM shift_swap_requests WHERE id = ?', [swap_id], one=True)
    if not swap_request or swap_request['user_id'] != user['id']:
        return jsonify({'success': False, 'error': 'طلب تبديل الدوام غير موجود أو ليس لديك صلاحية لإلغائه'})

    # Delete shift swap request
    db = get_db()
    db.execute('DELETE FROM shift_swap_requests WHERE id = ?', [swap_id])
    db.commit()

    return jsonify({'success': True})

@app.route('/other_requests')
def other_requests():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض الطلبات الأخرى', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get other requests based on role
    if user['role'] == 'manager':
        # Managers can see requests from their department
        other_requests = query_db('''
            SELECT req.*, u.first_name, u.last_name, u.username
            FROM other_requests req
            JOIN users u ON req.user_id = u.id
            WHERE u.department_id = ?
            ORDER BY req.created_at DESC
        ''', [user['department_id']])
    elif user['role'] in ['admin', 'hr', 'gm']:
        # Admin, HR, and GM can see all requests
        other_requests = query_db('''
            SELECT req.*, u.first_name, u.last_name, u.username, d.name as department_name
            FROM other_requests req
            JOIN users u ON req.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            ORDER BY req.created_at DESC
        ''')
    else:
        # Regular employees can see only their requests
        other_requests = query_db('''
            SELECT *
            FROM other_requests
            WHERE user_id = ?
            ORDER BY created_at DESC
        ''', [user['id']])

    return render_template('other_requests.html', user=user, other_requests=other_requests)

@app.route('/new_other_request', methods=['GET', 'POST'])
def new_other_request():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتقديم طلب جديد', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    if request.method == 'POST':
        title = request.form.get('title')
        description = request.form.get('description')

        # Insert other request
        db = get_db()
        db.execute('''
            INSERT INTO other_requests
            (user_id, title, description, status, created_at)
            VALUES (?, ?, ?, ?, ?)
        ''', [user['id'], title, description, 'pending', datetime.now()])
        db.commit()

        flash('تم تقديم الطلب بنجاح', 'success')
        return redirect(url_for('other_requests'))

    return render_template('new_other_request.html', user=user)

@app.route('/approve_other_request', methods=['POST'])
def approve_other_request():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للموافقة على الطلب', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    data = request.json
    request_id = data.get('request_id')
    approval_type = data.get('approval_type')
    action = data.get('action')

    # Check if user has permission to approve/reject
    if (approval_type == 'manager' and user['role'] != 'manager') or \
       (approval_type == 'hr' and user['role'] != 'hr') or \
       (approval_type == 'gm' and user['role'] != 'gm'):
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية للموافقة/الرفض'})

    # Get other request
    other_request = query_db('SELECT * FROM other_requests WHERE id = ?', [request_id], one=True)
    if not other_request:
        return jsonify({'success': False, 'error': 'الطلب غير موجود'})

    # Update other request
    db = get_db()
    if action == 'approve':
        if approval_type == 'manager':
            db.execute('UPDATE other_requests SET manager_approval = 1 WHERE id = ?', [request_id])
        elif approval_type == 'hr':
            db.execute('UPDATE other_requests SET hr_approval = 1 WHERE id = ?', [request_id])
        elif approval_type == 'gm':
            db.execute('UPDATE other_requests SET gm_approval = 1 WHERE id = ?', [request_id])

        # Check if all approvals are done
        other_request = query_db('SELECT * FROM other_requests WHERE id = ?', [request_id], one=True)
        if other_request['manager_approval'] == 1 and other_request['hr_approval'] == 1 and other_request['gm_approval'] == 1:
            db.execute('UPDATE other_requests SET status = "approved" WHERE id = ?', [request_id])
    else:  # reject
        if approval_type == 'manager':
            db.execute('UPDATE other_requests SET manager_approval = -1, status = "rejected" WHERE id = ?', [request_id])
        elif approval_type == 'hr':
            db.execute('UPDATE other_requests SET hr_approval = -1, status = "rejected" WHERE id = ?', [request_id])
        elif approval_type == 'gm':
            db.execute('UPDATE other_requests SET gm_approval = -1, status = "rejected" WHERE id = ?', [request_id])

    db.commit()
    return jsonify({'success': True})

@app.route('/cancel_other_request', methods=['POST'])
def cancel_other_request():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لإلغاء الطلب', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    data = request.json
    request_id = data.get('request_id')

    # Get other request
    other_request = query_db('SELECT * FROM other_requests WHERE id = ?', [request_id], one=True)
    if not other_request or other_request['user_id'] != user['id']:
        return jsonify({'success': False, 'error': 'الطلب غير موجود أو ليس لديك صلاحية لإلغائه'})

    # Delete other request
    db = get_db()
    db.execute('DELETE FROM other_requests WHERE id = ?', [request_id])
    db.commit()

    return jsonify({'success': True})

@app.route('/permissions')
def permissions():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض الأذونات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get permission requests for the user
    permission_requests = query_db('''
        SELECT * FROM permission_requests
        WHERE user_id = ?
        ORDER BY created_at DESC
    ''', [user['id']])

    return render_template('permissions.html', user=user, permission_requests=permission_requests)

@app.route('/permission/new', methods=['GET', 'POST'])
def new_permission():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لطلب إذن جديد', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    if request.method == 'POST':
        date = request.form.get('date')
        start_time = request.form.get('start_time')
        end_time = request.form.get('end_time')
        reason = request.form.get('reason')

        # Validate date and time
        try:
            date_obj = datetime.strptime(date, '%Y-%m-%d').date()
            start_time_obj = datetime.strptime(start_time, '%H:%M').time()
            end_time_obj = datetime.strptime(end_time, '%H:%M').time()

            if date_obj < datetime.now().date():
                flash('لا يمكن طلب إذن في تاريخ سابق', 'danger')
                return render_template('new_permission.html', user=user)

            if start_time_obj >= end_time_obj:
                flash('وقت البداية يجب أن يكون قبل وقت النهاية', 'danger')
                return render_template('new_permission.html', user=user)
        except ValueError:
            flash('صيغة التاريخ أو الوقت غير صحيحة', 'danger')
            return render_template('new_permission.html', user=user)

        # Insert permission request
        db = get_db()
        db.execute('''
            INSERT INTO permission_requests
            (user_id, date, start_time, end_time, reason, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', [user['id'], date, start_time, end_time, reason, 'pending', datetime.now()])
        db.commit()

        flash('تم تقديم طلب الإذن بنجاح', 'success')
        return redirect(url_for('permissions'))

    return render_template('new_permission.html', user=user)

# Function to add coverage compensation day
def add_coverage_compensation_day(user_id, coverage_date, shift_type):
    """إضافة يوم بدل تغطية إلى رصيد الموظف"""

    # Get coverage compensation leave type
    coverage_leave_type = query_db('SELECT * FROM leave_types WHERE name = ?', ['بدل يوم تغطية'], one=True)

    if not coverage_leave_type:
        print(f"تحذير: نوع إجازة 'بدل يوم تغطية' غير موجود")
        return

    current_year = datetime.now().year

    # Ensure leave balance exists for this user and leave type
    existing_balance = query_db('''
        SELECT * FROM leave_balances
        WHERE user_id = ? AND leave_type_id = ? AND year = ?
    ''', [user_id, coverage_leave_type['id'], current_year], one=True)

    db = get_db()

    if not existing_balance:
        # Create new balance
        db.execute('''
            INSERT INTO leave_balances (user_id, leave_type_id, year, total_days, used_days, remaining_days)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', [user_id, coverage_leave_type['id'], current_year, 1, 0, 1])
    else:
        # Update existing balance - add one day
        new_total = existing_balance['total_days'] + 1
        new_remaining = existing_balance['remaining_days'] + 1

        db.execute('''
            UPDATE leave_balances
            SET total_days = ?, remaining_days = ?
            WHERE user_id = ? AND leave_type_id = ? AND year = ?
        ''', [new_total, new_remaining, user_id, coverage_leave_type['id'], current_year])

    # Add audit log (simplified - no audit_logs table dependency)
    try:
        user_info = query_db('SELECT first_name, last_name FROM users WHERE id = ?', [user_id], one=True)
        coverage_date_obj = datetime.strptime(coverage_date, '%Y-%m-%d')

        # Determine coverage type description
        coverage_type_desc = {
            'morning': 'الفترة الصباحية',
            'evening': 'الفترة المسائية',
            'night': 'الفترة الليلية',
            'full_day': 'يوم كامل'
        }.get(shift_type, shift_type)

        # Check if it's a Friday or holiday
        is_friday = coverage_date_obj.weekday() == 4
        day_type = "يوم الجمعة" if is_friday else "يوم عادي"

        # Try to insert audit log, but don't fail if table doesn't exist
        try:
            db.execute('''
                INSERT INTO audit_logs
                (user_id, action, entity_type, entity_id, details, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', [user_id, 'add_coverage_compensation', 'leave_balance', coverage_leave_type['id'],
                  f"تم إضافة يوم بدل تغطية للموظف {user_info['first_name']} {user_info['last_name']} "
                  f"عن تغطية {coverage_type_desc} في تاريخ {coverage_date} ({day_type})",
                  datetime.now()])
        except sqlite3.OperationalError:
            # audit_logs table doesn't exist, skip logging
            pass
    except Exception as e:
        print(f"خطأ في تسجيل السجل: {e}")

    db.commit()

# Function to use coverage compensation days
def use_coverage_compensation_days(user_id, days_to_use):
    """استخدام أيام بدل التغطية من رصيد الموظف"""

    # Get coverage compensation leave type
    coverage_leave_type = query_db('SELECT * FROM leave_types WHERE name = ?', ['بدل يوم تغطية'], one=True)

    if not coverage_leave_type:
        print(f"تحذير: نوع إجازة 'بدل يوم تغطية' غير موجود")
        return False

    current_year = datetime.now().year

    # Get current balance
    balance = query_db('''
        SELECT * FROM leave_balances
        WHERE user_id = ? AND leave_type_id = ? AND year = ?
    ''', [user_id, coverage_leave_type['id'], current_year], one=True)

    if not balance:
        print(f"تحذير: لا يوجد رصيد بدل تغطية للموظف {user_id}")
        return False

    if balance['remaining_days'] < days_to_use:
        print(f"تحذير: رصيد بدل التغطية غير كافي للموظف {user_id}")
        return False

    # Update balance
    db = get_db()
    new_used = balance['used_days'] + days_to_use
    new_remaining = balance['remaining_days'] - days_to_use

    db.execute('''
        UPDATE leave_balances
        SET used_days = ?, remaining_days = ?
        WHERE user_id = ? AND leave_type_id = ? AND year = ?
    ''', [new_used, new_remaining, user_id, coverage_leave_type['id'], current_year])

    # Add audit log (simplified - no audit_logs table dependency)
    try:
        user_info = query_db('SELECT first_name, last_name FROM users WHERE id = ?', [user_id], one=True)

        # Try to insert audit log, but don't fail if table doesn't exist
        try:
            db.execute('''
                INSERT INTO audit_logs
                (user_id, action, entity_type, entity_id, details, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', [user_id, 'use_coverage_compensation', 'leave_balance', coverage_leave_type['id'],
                  f"تم استخدام {days_to_use} يوم من رصيد بدل التغطية للموظف {user_info['first_name']} {user_info['last_name']}",
                  datetime.now()])
        except sqlite3.OperationalError:
            # audit_logs table doesn't exist, skip logging
            pass
    except Exception as e:
        print(f"خطأ في تسجيل السجل: {e}")

    db.commit()
    return True

# Function to ensure leave balances exist for a user
def ensure_leave_balances(user_id):
    # Get all leave types
    leave_types = query_db('SELECT * FROM leave_types')
    current_year = datetime.now().year

    # Get existing balances for the user
    existing_balances = query_db('''
        SELECT leave_type_id FROM leave_balances
        WHERE user_id = ? AND year = ?
    ''', [user_id, current_year])

    # Convert to a set for faster lookup
    existing_leave_type_ids = set()
    for balance in existing_balances:
        existing_leave_type_ids.add(balance['leave_type_id'])

    # Create missing balances
    db = get_db()
    for leave_type in leave_types:
        # Skip leave types that don't have default balances
        if leave_type['name'] in ['إجازة بدون راتب', 'إجازة تغطية']:
            continue

        if leave_type['id'] not in existing_leave_type_ids:
            # Special handling for coverage compensation leave type
            if leave_type['name'] == 'بدل يوم تغطية':
                # Start with 0 days for coverage compensation
                db.execute('''
                    INSERT INTO leave_balances
                    (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', [user_id, leave_type['id'], current_year, 0, 0, 0])
            else:
                # Normal leave types with default days
                db.execute('''
                    INSERT INTO leave_balances
                    (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', [user_id, leave_type['id'], current_year, leave_type['default_days'], 0, leave_type['default_days']])

    db.commit()

# Initialize database
def create_tables():
    try:
        # Check if tables exist
        query_db('SELECT 1 FROM users LIMIT 1')
    except sqlite3.OperationalError:
        # Tables don't exist, initialize database
        init_db()

        # Create admin user
        db = get_db()
        db.execute('''
            INSERT INTO users (username, email, password_hash, first_name, last_name, role, department_id, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', ['admin', '<EMAIL>', hash_password('admin123'), 'مدير', 'النظام', 'admin', 1, 1])

        # Create supervisory accounts
        # General Manager
        db.execute('''
            INSERT INTO users (username, email, password_hash, first_name, last_name, role, department_id, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', ['gm', '<EMAIL>', hash_password('gm123'), 'المدير', 'العام', 'gm', 1, 1])

        # HR Manager
        db.execute('''
            INSERT INTO users (username, email, password_hash, first_name, last_name, role, department_id, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', ['hr', '<EMAIL>', hash_password('hr123'), 'مدير', 'الموارد البشرية', 'hr', 1, 1])

        # Lab Manager (only one manager)
        db.execute('''
            INSERT INTO users (username, email, password_hash, first_name, last_name, role, department_id, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', ['lab_manager', '<EMAIL>', hash_password('manager123'), 'مدير', 'المختبر', 'manager', 1, 1])

        # Create 15 employee accounts with sequential numbering
        for i in range(1, 16):
            db.execute('''
                INSERT INTO users (username, email, password_hash, first_name, last_name, role, department_id, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', [f'emp{i}', f'emp{i}@alemis.com', hash_password('emp123'), f'موظف', f'{i}', 'employee', 1, 1])

        db.commit()

        # Create leave balances for all users
        users = query_db('SELECT id FROM users')
        for user in users:
            ensure_leave_balances(user['id'])

@app.route('/init')
def initialize_database():
    create_tables()
    return 'تم تهيئة قاعدة البيانات بنجاح'

@app.route('/add_coverage_leave_type')
def add_coverage_leave_type():
    """إضافة نوع إجازة بدل يوم تغطية"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user['role'] not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    db = get_db()

    # Check if 'بدل يوم تغطية' leave type already exists
    existing = query_db('SELECT * FROM leave_types WHERE name = ?', ['بدل يوم تغطية'], one=True)

    if not existing:
        # Add new leave type
        db.execute('''
            INSERT INTO leave_types (name, description, default_days, requires_approval, max_consecutive_days)
            VALUES (?, ?, ?, ?, ?)
        ''', ['بدل يوم تغطية', 'إجازة بدل أيام التغطية والعمل في الإجازات الرسمية', 0, 1, 30])

        # Get the new leave type ID
        leave_type_id = db.lastrowid

        # Create balances for all existing users
        users = query_db('SELECT id FROM users')
        current_year = datetime.now().year

        for user_data in users:
            user_id = user_data['id']
            # Check if balance already exists
            existing_balance = query_db('''
                SELECT * FROM leave_balances
                WHERE user_id = ? AND leave_type_id = ? AND year = ?
            ''', [user_id, leave_type_id, current_year], one=True)

            if not existing_balance:
                db.execute('''
                    INSERT INTO leave_balances (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', [user_id, leave_type_id, current_year, 0, 0, 0])

        db.commit()
        flash(f'تم إضافة نوع إجازة بدل يوم تغطية وإنشاء أرصدة لـ {len(users)} موظف', 'success')
    else:
        flash('نوع إجازة بدل يوم تغطية موجود بالفعل', 'info')

    return redirect(url_for('leave_balances'))

@app.route('/remove_coverage_leave_type')
def remove_coverage_leave_type():
    """حذف نوع إجازة 'إجازة تغطية' من قاعدة البيانات"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user['role'] not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    db = get_db()

    # Check if 'إجازة تغطية' leave type exists
    existing = query_db('SELECT * FROM leave_types WHERE name = ?', ['إجازة تغطية'], one=True)

    if existing:
        leave_type_id = existing['id']

        # Check if there are any leave requests using this type
        request_count = query_db('SELECT COUNT(*) as count FROM leave_requests WHERE leave_type_id = ?', [leave_type_id], one=True)['count']

        if request_count > 0:
            # Delete leave requests
            db.execute('DELETE FROM leave_requests WHERE leave_type_id = ?', [leave_type_id])

        # Check if there are any leave balances using this type
        balance_count = query_db('SELECT COUNT(*) as count FROM leave_balances WHERE leave_type_id = ?', [leave_type_id], one=True)['count']

        if balance_count > 0:
            # Delete leave balances
            db.execute('DELETE FROM leave_balances WHERE leave_type_id = ?', [leave_type_id])

        # Delete the leave type
        db.execute('DELETE FROM leave_types WHERE id = ?', [leave_type_id])

        db.commit()
        flash(f'تم حذف نوع إجازة "إجازة تغطية" بنجاح. تم حذف {request_count} طلب و {balance_count} رصيد', 'success')
    else:
        flash('نوع إجازة "إجازة تغطية" غير موجود', 'info')

    return redirect(url_for('leave_balances'))

@app.route('/reset_leave_balances')
def reset_leave_balances():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لإعادة تهيئة أرصدة الإجازات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to reset leave balances
    if user['role'] not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get all users
    users = query_db('SELECT id FROM users')

    # Reset leave balances for all users
    db = get_db()

    # First, delete all existing balances for the current year
    db.execute('DELETE FROM leave_balances WHERE year = ?', [datetime.now().year])
    db.commit()

    # Then create new balances for all users
    for user_data in users:
        ensure_leave_balances(user_data['id'])

    flash('تم إعادة تهيئة أرصدة الإجازات بنجاح', 'success')
    return redirect(url_for('leave_balances'))

@app.route('/users')
def users():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى إدارة المستخدمين', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view users
    if user['role'] not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get all users
    users = query_db('''
        SELECT u.*, d.name as department_name
        FROM users u
        LEFT JOIN departments d ON u.department_id = d.id
        ORDER BY u.role, u.first_name, u.last_name
    ''')

    # Get departments for the form
    departments = query_db('SELECT * FROM departments ORDER BY name')

    return render_template('users.html', user=user, users=users, departments=departments)

@app.route('/users/new', methods=['GET', 'POST'])
def new_user():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لإضافة مستخدم جديد', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to add users
    if user['role'] not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get departments for the form
    departments = query_db('SELECT * FROM departments ORDER BY name')

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        password = request.form.get('password')
        role = request.form.get('role')
        department_id = request.form.get('department_id')
        is_active = 1 if request.form.get('is_active') else 0

        # Check if username already exists
        existing_user = query_db('SELECT * FROM users WHERE username = ?', [username], one=True)
        if existing_user:
            flash('اسم المستخدم موجود بالفعل', 'danger')
            return render_template('new_user.html', user=user, departments=departments)

        # Check if email already exists
        existing_email = query_db('SELECT * FROM users WHERE email = ?', [email], one=True)
        if existing_email:
            flash('البريد الإلكتروني موجود بالفعل', 'danger')
            return render_template('new_user.html', user=user, departments=departments)

        # Insert new user
        db = get_db()
        db.execute('''
            INSERT INTO users
            (username, email, password_hash, first_name, last_name, role, department_id, is_active, date_joined)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', [username, email, hash_password(password), first_name, last_name, role, department_id, is_active, datetime.now()])
        db.commit()

        # Create leave balances for the new user
        user_id = query_db('SELECT id FROM users WHERE username = ?', [username], one=True)['id']
        leave_types = query_db('SELECT * FROM leave_types')

        for leave_type in leave_types:
            # Skip leave types that don't have default balances
            if leave_type['name'] in ['إجازة بدون راتب', 'إجازة تغطية']:
                continue

            # Special handling for coverage compensation leave type
            if leave_type['name'] == 'بدل يوم تغطية':
                # Start with 0 days for coverage compensation
                db.execute('''
                    INSERT INTO leave_balances
                    (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', [user_id, leave_type['id'], datetime.now().year, 0, 0, 0])
            else:
                # Normal leave types with default days
                db.execute('''
                    INSERT INTO leave_balances
                    (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', [user_id, leave_type['id'], datetime.now().year, leave_type['default_days'], 0, leave_type['default_days']])

        db.commit()

        flash('تم إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users'))

    return render_template('new_user.html', user=user, departments=departments)

@app.route('/users/edit/<int:user_id>', methods=['GET', 'POST'])
def edit_user(user_id):
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتعديل المستخدم', 'warning')
        return redirect(url_for('login'))

    current_user = get_current_user()

    # Check if user has permission to edit users
    if current_user['role'] not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get user to edit
    user_to_edit = query_db('SELECT * FROM users WHERE id = ?', [user_id], one=True)

    if not user_to_edit:
        flash('المستخدم غير موجود', 'danger')
        return redirect(url_for('users'))

    # Get departments for the form
    departments = query_db('SELECT * FROM departments ORDER BY name')

    if request.method == 'POST':
        email = request.form.get('email')
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        role = request.form.get('role')
        department_id = request.form.get('department_id')
        is_active = 1 if request.form.get('is_active') else 0

        # Check if email already exists (for another user)
        existing_email = query_db('SELECT * FROM users WHERE email = ? AND id != ?', [email, user_id], one=True)
        if existing_email:
            flash('البريد الإلكتروني موجود بالفعل', 'danger')
            return render_template('edit_user.html', user=current_user, user_to_edit=user_to_edit, departments=departments)

        # Update user
        db = get_db()
        db.execute('''
            UPDATE users
            SET email = ?, first_name = ?, last_name = ?, role = ?, department_id = ?, is_active = ?
            WHERE id = ?
        ''', [email, first_name, last_name, role, department_id, is_active, user_id])

        # Update password if provided
        password = request.form.get('password')
        if password:
            db.execute('UPDATE users SET password_hash = ? WHERE id = ?', [hash_password(password), user_id])

        db.commit()

        flash('تم تعديل المستخدم بنجاح', 'success')
        return redirect(url_for('users'))

    return render_template('edit_user.html', user=current_user, user_to_edit=user_to_edit, departments=departments)

@app.route('/users/delete/<int:user_id>', methods=['POST'])
def delete_user(user_id):
    if not is_authenticated():
        flash('يجب تسجيل الدخول لحذف المستخدم', 'warning')
        return redirect(url_for('login'))

    current_user = get_current_user()

    # Check if user has permission to delete users
    if current_user['role'] not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية لحذف المستخدمين', 'danger')
        return redirect(url_for('dashboard'))

    # Get user to delete
    user_to_delete = query_db('SELECT * FROM users WHERE id = ?', [user_id], one=True)

    if not user_to_delete:
        flash('المستخدم غير موجود', 'danger')
        return redirect(url_for('users'))

    # Prevent deleting admin users
    if user_to_delete['role'] == 'admin' and current_user['role'] != 'admin':
        flash('لا يمكن حذف حساب مدير النظام', 'danger')
        return redirect(url_for('users'))

    # Prevent deleting your own account
    if user_to_delete['id'] == current_user['id']:
        flash('لا يمكن حذف حسابك الخاص', 'danger')
        return redirect(url_for('users'))

    # Delete user
    db = get_db()

    # Delete related records first
    db.execute('DELETE FROM leave_balances WHERE user_id = ?', [user_id])
    db.execute('DELETE FROM leave_requests WHERE user_id = ?', [user_id])
    db.execute('DELETE FROM coverage_requests WHERE user_id = ? OR coverage_user_id = ?', [user_id, user_id])
    db.execute('DELETE FROM shift_swap_requests WHERE user_id = ? OR swap_with_user_id = ?', [user_id, user_id])
    db.execute('DELETE FROM other_requests WHERE user_id = ?', [user_id])
    db.execute('DELETE FROM permission_requests WHERE user_id = ?', [user_id])

    # Finally delete the user
    db.execute('DELETE FROM users WHERE id = ?', [user_id])
    db.commit()

    flash('تم حذف المستخدم بنجاح', 'success')
    return redirect(url_for('users'))

@app.route('/departments')
def departments():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى إدارة الأقسام', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view departments
    if user['role'] not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get all departments
    departments = query_db('''
        SELECT d.*, COUNT(u.id) as employee_count
        FROM departments d
        LEFT JOIN users u ON u.department_id = d.id
        GROUP BY d.id
        ORDER BY d.name
    ''')

    # Get managers for the form
    managers = query_db('''
        SELECT * FROM users
        WHERE role = 'manager'
        ORDER BY first_name, last_name
    ''')

    return render_template('departments.html', user=user, departments=departments, managers=managers)

@app.route('/departments/new', methods=['GET', 'POST'])
def new_department():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لإضافة قسم جديد', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to add departments
    if user['role'] not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get managers for the form
    managers = query_db('''
        SELECT * FROM users
        WHERE role = 'manager'
        ORDER BY first_name, last_name
    ''')

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')

        # Check if department name already exists
        existing_dept = query_db('SELECT * FROM departments WHERE name = ?', [name], one=True)
        if existing_dept:
            flash('اسم القسم موجود بالفعل', 'danger')
            return render_template('new_department.html', user=user, managers=managers)

        # Insert new department
        db = get_db()
        db.execute('''
            INSERT INTO departments
            (name, description)
            VALUES (?, ?)
        ''', [name, description])
        db.commit()

        flash('تم إضافة القسم بنجاح', 'success')
        return redirect(url_for('departments'))

    return render_template('new_department.html', user=user, managers=managers)

@app.route('/departments/edit/<int:dept_id>', methods=['GET', 'POST'])
def edit_department(dept_id):
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتعديل القسم', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to edit departments
    if user['role'] not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get department to edit
    dept_to_edit = query_db('SELECT * FROM departments WHERE id = ?', [dept_id], one=True)

    if not dept_to_edit:
        flash('القسم غير موجود', 'danger')
        return redirect(url_for('departments'))

    # Get managers for the form
    managers = query_db('''
        SELECT * FROM users
        WHERE role = 'manager'
        ORDER BY first_name, last_name
    ''')

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')

        # Check if department name already exists (for another department)
        existing_dept = query_db('SELECT * FROM departments WHERE name = ? AND id != ?', [name, dept_id], one=True)
        if existing_dept:
            flash('اسم القسم موجود بالفعل', 'danger')
            return render_template('edit_department.html', user=user, dept=dept_to_edit, managers=managers)

        # Update department
        db = get_db()
        db.execute('''
            UPDATE departments
            SET name = ?, description = ?
            WHERE id = ?
        ''', [name, description, dept_id])
        db.commit()

        flash('تم تعديل القسم بنجاح', 'success')
        return redirect(url_for('departments'))

    return render_template('edit_department.html', user=user, dept=dept_to_edit, managers=managers)

@app.route('/leave/balances')
def leave_balances():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض أرصدة الإجازات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view leave balances
    if user['role'] not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get all users with their leave balances
    users_with_balances_rows = query_db('''
        SELECT u.id, u.first_name, u.last_name, u.username, d.name as department_name
        FROM users u
        LEFT JOIN departments d ON u.department_id = d.id
        WHERE u.role NOT IN ('admin', 'hr', 'manager', 'gm')
        ORDER BY u.first_name, u.last_name
    ''')

    # Convert sqlite3.Row objects to dictionaries
    users_with_balances = []
    for user_row in users_with_balances_rows:
        users_with_balances.append(dict(user_row))

    # Get leave types (include coverage compensation leave type)
    leave_types = query_db('SELECT * FROM leave_types WHERE name NOT IN ("إجازة بدون راتب", "إجازة تغطية")')

    # Ensure leave balances exist for all users
    for user_data in users_with_balances:
        ensure_leave_balances(user_data['id'])

    # Get leave balances for each user
    for user_data in users_with_balances:
        balances_rows = query_db('''
            SELECT lb.*, lt.name as leave_type_name
            FROM leave_balances lb
            JOIN leave_types lt ON lb.leave_type_id = lt.id
            WHERE lb.user_id = ? AND lb.year = ?
        ''', [user_data['id'], datetime.now().year])

        # Convert sqlite3.Row objects to dictionaries
        balances = []
        for balance_row in balances_rows:
            balances.append(dict(balance_row))

        user_data['balances'] = {}
        for balance in balances:
            user_data['balances'][balance['leave_type_id']] = balance

    return render_template('leave_balances.html', user=user, users=users_with_balances, leave_types=leave_types, current_year=datetime.now().year)

@app.route('/leave/balance/edit/<int:user_id>/<int:leave_type_id>', methods=['POST'])
def edit_leave_balance(user_id, leave_type_id):
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتعديل رصيد الإجازة', 'warning')
        return redirect(url_for('login'))

    current_user = get_current_user()

    # Check if user has permission to edit leave balances
    if current_user['role'] not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    total_days = int(request.form.get('total_days', 0))
    used_days = int(request.form.get('used_days', 0))
    remaining_days = total_days - used_days

    # Update leave balance
    db = get_db()
    db.execute('''
        UPDATE leave_balances
        SET total_days = ?, used_days = ?, remaining_days = ?
        WHERE user_id = ? AND leave_type_id = ? AND year = ?
    ''', [total_days, used_days, remaining_days, user_id, leave_type_id, datetime.now().year])
    db.commit()

    flash('تم تعديل رصيد الإجازة بنجاح', 'success')
    return redirect(url_for('leave_balances'))





@app.route('/reports/statistics', methods=['GET', 'POST'])
def statistics_report():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض تقرير الإحصائيات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view reports
    if user['role'] not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get current year
    current_year = datetime.now().year

    # Get leave statistics by month
    monthly_stats = query_db('''
        SELECT strftime('%m', start_date) as month, COUNT(id) as leave_count
        FROM leave_requests
        WHERE status = 'approved' AND strftime('%Y', start_date) = ?
        GROUP BY month
        ORDER BY month
    ''', [str(current_year)])

    # Convert month numbers to names
    month_names = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']

    # Convert sqlite3.Row objects to dictionaries and add month_name
    monthly_stats_with_names = []
    for stat in monthly_stats:
        stat_dict = dict(stat)
        stat_dict['month_name'] = month_names[int(stat['month']) - 1]
        monthly_stats_with_names.append(stat_dict)

    monthly_stats = monthly_stats_with_names

    # Get leave statistics by type
    leave_type_stats = query_db('''
        SELECT lt.name as leave_type_name, COUNT(lr.id) as leave_count
        FROM leave_types lt
        LEFT JOIN leave_requests lr ON lr.leave_type_id = lt.id AND lr.status = 'approved'
        GROUP BY lt.id
        ORDER BY leave_count DESC
    ''')

    # Get coverage statistics
    coverage_stats = query_db('''
        SELECT COUNT(*) as total_count,
               SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
               SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
               SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count
        FROM coverage_requests
    ''', one=True)

    # Get department statistics
    department_stats = query_db('''
        SELECT d.name as department_name, COUNT(u.id) as employee_count
        FROM departments d
        LEFT JOIN users u ON u.department_id = d.id
        WHERE u.is_active = 1
        GROUP BY d.id
        ORDER BY employee_count DESC
    ''')

    # Default filters
    filters = {
        'year': request.form.get('year', current_year)
    }

    # إضافة البيانات المطلوبة للرسوم البيانية
    leaves_by_month = [0] * 12
    for stat in monthly_stats:
        month_index = int(stat['month']) - 1
        leaves_by_month[month_index] = stat['leave_count']

    leave_type_labels = [stat['leave_type_name'] for stat in leave_type_stats]
    leave_type_data = [stat['leave_count'] for stat in leave_type_stats]

    department_labels = [stat['department_name'] for stat in department_stats]
    department_data = [stat['employee_count'] for stat in department_stats]

    # بيانات وهمية لمعدل الموافقة
    approval_rate_data = [85, 90, 88, 92, 87, 89, 91, 86, 88, 90, 89, 87]

    # إحصائيات إضافية
    total_leaves = sum(stat['leave_count'] for stat in monthly_stats)
    total_leave_days = query_db('''
        SELECT SUM(JULIANDAY(end_date) - JULIANDAY(start_date) + 1) as total_days
        FROM leave_requests
        WHERE status = 'approved' AND strftime('%Y', start_date) = ?
    ''', [str(current_year)], one=True)['total_days'] or 0

    total_coverage_requests = coverage_stats['total_count'] if coverage_stats else 0

    # التحقق من وجود جدول permissions
    try:
        total_permissions = query_db('SELECT COUNT(*) as count FROM permissions', one=True)['count']
    except:
        total_permissions = 0

    return render_template('statistics_report.html', user=user,
                          monthly_stats=monthly_stats, leave_type_stats=leave_type_stats,
                          coverage_stats=coverage_stats, department_stats=department_stats,
                          filters=filters, leaves_by_month=leaves_by_month,
                          leave_type_labels=leave_type_labels, leave_type_data=leave_type_data,
                          department_labels=department_labels, department_data=department_data,
                          approval_rate_data=approval_rate_data, total_leaves=total_leaves,
                          total_leave_days=total_leave_days, total_coverage_requests=total_coverage_requests,
                          total_permissions=total_permissions, current_year=current_year)

@app.route('/reports/employees', methods=['GET', 'POST'])
def employee_report():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض تقرير الموظفين', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view reports
    if user['role'] not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get departments for filtering
    departments = query_db('SELECT * FROM departments ORDER BY name')

    # Default filters
    filters = {
        'department_id': request.form.get('department_id', 'all'),
        'role': request.form.get('role', 'all'),
        'status': request.form.get('status', 'all')
    }

    # Build query based on filters
    query = '''
        SELECT u.*, d.name as department_name
        FROM users u
        JOIN departments d ON u.department_id = d.id
        WHERE 1=1
    '''
    params = []

    if filters['department_id'] != 'all':
        query += ' AND u.department_id = ?'
        params.append(filters['department_id'])

    if filters['role'] != 'all':
        query += ' AND u.role = ?'
        params.append(filters['role'])

    if filters['status'] != 'all':
        is_active = 1 if filters['status'] == 'active' else 0
        query += ' AND u.is_active = ?'
        params.append(is_active)

    query += ' ORDER BY u.first_name, u.last_name'

    # Execute query
    employees = query_db(query, params)

    # Get leave balances
    leave_balances = query_db('''
        SELECT lb.*, lt.name as leave_type_name, u.id as user_id
        FROM leave_balances lb
        JOIN leave_types lt ON lb.leave_type_id = lt.id
        JOIN users u ON lb.user_id = u.id
        WHERE lb.year = ?
    ''', [datetime.now().year])

    # Get statistics
    total_employees = len(employees)
    active_employees = sum(1 for emp in employees if emp['is_active'] == 1)
    inactive_employees = total_employees - active_employees

    # Get employees on leave
    on_leave_employees = query_db('''
        SELECT COUNT(DISTINCT u.id) as count
        FROM users u
        JOIN leave_requests lr ON u.id = lr.user_id
        WHERE lr.status = 'approved'
        AND ? BETWEEN lr.start_date AND lr.end_date
    ''', [datetime.now().strftime('%Y-%m-%d')], one=True)['count']

    # Get department distribution
    dept_counts = {}
    for emp in employees:
        dept_name = emp['department_name']
        if dept_name in dept_counts:
            dept_counts[dept_name] += 1
        else:
            dept_counts[dept_name] = 1

    department_labels = list(dept_counts.keys())
    department_data = list(dept_counts.values())

    # Get role distribution
    role_counts = {
        'admin': 0,
        'hr': 0,
        'manager': 0,
        'gm': 0,
        'employee': 0
    }

    for emp in employees:
        role = emp['role']
        if role in role_counts:
            role_counts[role] += 1

    role_labels = ['مدير النظام', 'موارد بشرية', 'مدير قسم', 'مدير عام', 'موظف']
    role_data = [role_counts['admin'], role_counts['hr'], role_counts['manager'], role_counts['gm'], role_counts['employee']]

    return render_template('employee_report.html', user=user, departments=departments,
                          employees=employees, leave_balances=leave_balances, filters=filters,
                          total_employees=total_employees, active_employees=active_employees,
                          inactive_employees=inactive_employees, on_leave_employees=on_leave_employees,
                          department_labels=department_labels, department_data=department_data,
                          role_labels=role_labels, role_data=role_data)

@app.route('/reports/leaves', methods=['GET', 'POST'])
def leave_report():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض تقرير الإجازات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view reports
    if user['role'] not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get departments for filtering
    departments = query_db('SELECT * FROM departments ORDER BY name')

    # Get leave types for filtering
    leave_types = query_db('SELECT * FROM leave_types ORDER BY name')

    # Default filters
    filters = {
        'department_id': request.form.get('department_id', 'all'),
        'leave_type_id': request.form.get('leave_type_id', 'all'),
        'status': request.form.get('status', 'all'),
        'start_date': request.form.get('start_date', ''),
        'end_date': request.form.get('end_date', '')
    }

    # Build query based on filters
    query = '''
        SELECT lr.*, lt.name as leave_type_name, u.first_name, u.last_name, u.username, d.name as department_name
        FROM leave_requests lr
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        JOIN users u ON lr.user_id = u.id
        JOIN departments d ON u.department_id = d.id
        WHERE 1=1
    '''
    params = []

    if filters['department_id'] != 'all':
        query += ' AND u.department_id = ?'
        params.append(filters['department_id'])

    if filters['leave_type_id'] != 'all':
        query += ' AND lr.leave_type_id = ?'
        params.append(filters['leave_type_id'])

    if filters['status'] != 'all':
        query += ' AND lr.status = ?'
        params.append(filters['status'])

    if filters['start_date']:
        query += ' AND lr.start_date >= ?'
        params.append(filters['start_date'])

    if filters['end_date']:
        query += ' AND lr.end_date <= ?'
        params.append(filters['end_date'])

    query += ' ORDER BY lr.created_at DESC'

    # Execute query
    leave_requests = query_db(query, params)

    return render_template('leave_report.html', user=user, departments=departments, leave_types=leave_types,
                          leave_requests=leave_requests, filters=filters)

@app.route('/schedule')
def schedule():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض الجداول الشهرية', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view schedules
    if user['role'] not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get current month and year
    current_month = datetime.now().month
    current_year = datetime.now().year

    # Get month from query parameters
    month = int(request.args.get('month', current_month))
    year = int(request.args.get('year', current_year))

    # Get first day of the month
    first_day = datetime(year, month, 1)

    # Get number of days in the month
    if month == 12:
        last_day = datetime(year + 1, 1, 1) - timedelta(days=1)
    else:
        last_day = datetime(year, month + 1, 1) - timedelta(days=1)

    num_days = last_day.day

    # Get day of week for the first day (0 = Monday, 6 = Sunday)
    first_weekday = first_day.weekday()

    # Adjust for Sunday as first day of week
    first_weekday = (first_weekday + 1) % 7

    # Get departments
    departments = query_db('SELECT * FROM departments ORDER BY name')

    # Get selected department
    department_id = int(request.args.get('department_id', user['department_id'] if user['department_id'] else 1))

    # Get employees in the department
    employees = query_db('''
        SELECT * FROM users
        WHERE department_id = ? AND is_active = 1
        ORDER BY first_name, last_name
    ''', [department_id])

    # Get approved leaves for the month
    leaves = query_db('''
        SELECT lr.*, u.first_name, u.last_name, lt.name as leave_type_name
        FROM leave_requests lr
        JOIN users u ON lr.user_id = u.id
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        WHERE lr.status = 'approved'
        AND u.department_id = ?
        AND (
            (strftime('%Y-%m', lr.start_date) <= ? AND strftime('%Y-%m', lr.end_date) >= ?)
            OR
            (strftime('%Y-%m', lr.start_date) = ?)
        )
    ''', [department_id, f"{year}-{month:02d}", f"{year}-{month:02d}", f"{year}-{month:02d}"])

    # Create calendar data
    calendar_data = {}
    for employee in employees:
        calendar_data[employee['id']] = {
            'name': f"{employee['first_name']} {employee['last_name']}",
            'days': [None] * num_days
        }

    # Fill in leaves
    for leave in leaves:
        start_date = datetime.strptime(leave['start_date'], '%Y-%m-%d')
        end_date = datetime.strptime(leave['end_date'], '%Y-%m-%d')

        # Adjust dates to be within the current month
        if start_date.year < year or (start_date.year == year and start_date.month < month):
            start_date = first_day

        if end_date.year > year or (end_date.year == year and end_date.month > month):
            end_date = last_day

        # Fill in the days
        for day in range(start_date.day, end_date.day + 1):
            if 1 <= day <= num_days:
                calendar_data[leave['user_id']]['days'][day - 1] = {
                    'type': leave['leave_type_name'],
                    'id': leave['id']
                }

    # Get month name
    month_names = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
    month_name = month_names[month - 1]

    return render_template('schedule.html', user=user, departments=departments, department_id=department_id,
                          year=year, month=month, month_name=month_name, num_days=num_days, first_weekday=first_weekday,
                          calendar_data=calendar_data, employees=employees)

@app.route('/my_shift_schedule')
def my_shift_schedule():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض جدول دوامك', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get current month and year or from query parameters
    month = request.args.get('month', datetime.now().month, type=int)
    year = request.args.get('year', datetime.now().year, type=int)

    # Get days in month
    days_in_month = calendar.monthrange(year, month)[1]

    # Get number of days in the month
    num_days = calendar.monthrange(year, month)[1]

    # Get day of week for each day of the month (0 = Monday, 6 = Sunday)
    day_of_week = []
    for day in range(1, num_days + 1):
        day_date = datetime(year, month, day)
        weekday = day_date.weekday()
        # Adjust for Sunday as first day of week (0 = Saturday, 1 = Sunday, ..., 6 = Friday)
        day_of_week.append((weekday + 1) % 7)

    # Get shift schedule for the month
    schedule = query_db('''
        SELECT * FROM shift_schedules
        WHERE month = ? AND year = ?
    ''', [month, year], one=True)

    # Get schedule status
    schedule_status = 'draft'
    if schedule:
        schedule_status = schedule['status']

        # Get shift assignments for the user
        assignments = query_db('''
            SELECT * FROM shift_assignments
            WHERE schedule_id = ? AND user_id = ?
        ''', [schedule['id'], user['id']])

        # Create shift data
        shift_data = {}
        for assignment in assignments:
            shift_data[assignment['day']] = assignment['shift_type']
    else:
        shift_data = {}

    # Create calendar data
    calendar_data = []
    for day in range(1, days_in_month + 1):
        date_obj = date(year, month, day)
        weekday = date_obj.weekday()  # 0 is Monday, 6 is Sunday

        # Convert to Arabic week (Sunday is first day)
        arabic_weekday = (weekday + 1) % 7

        # Get shift for this day
        shift = shift_data.get(day, None)

        calendar_data.append({
            'day': day,
            'weekday': arabic_weekday,
            'shift': shift,
            'is_weekend': arabic_weekday == 5,  # Friday is weekend
            'date': date_obj.strftime('%Y-%m-%d')
        })

    # Get month name in Arabic
    month_names = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
    month_name = month_names[month - 1]

    return render_template('my_shift_schedule.html', user=user,
                          calendar_data=calendar_data,
                          month=month, year=year, current_year=year, current_month=month,
                          month_name=month_name, num_days=num_days, day_of_week=day_of_week,
                          schedule_status=schedule_status, shift_data=shift_data)

@app.route('/direct_shift_swap', methods=['GET', 'POST'])
def direct_shift_swap():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتبديل الدوام', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get current month and year
    month = datetime.now().month
    year = datetime.now().year

    # Get shift schedule for the month
    schedule = query_db('''
        SELECT * FROM shift_schedules
        WHERE month = ? AND year = ?
    ''', [month, year], one=True)

    # Get schedule status
    schedule_status = 'draft'
    if schedule:
        schedule_status = schedule['status']

    # Get number of days in the month
    num_days = calendar.monthrange(year, month)[1]

    # Get day of week for each day of the month (0 = Monday, 6 = Sunday)
    day_of_week = []
    for day in range(1, num_days + 1):
        day_date = datetime(year, month, day)
        weekday = day_date.weekday()
        # Adjust for Sunday as first day of week (0 = Saturday, 1 = Sunday, ..., 6 = Friday)
        day_of_week.append((weekday + 1) % 7)

    # Get month name
    month_names = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
    month_name = month_names[month - 1]

    # Get all employees for swap selection
    employees = query_db('''
        SELECT id, first_name, last_name
        FROM users
        WHERE role = 'employee' AND is_active = 1 AND id != ?
        ORDER BY first_name, last_name
    ''', [user['id']])

    if request.method == 'POST':
        swap_type = request.form.get('swap_type')
        swap_with_user_id = request.form.get('swap_with_user_id')

        if swap_type == 'day_swap':
            my_days = request.form.getlist('my_days')
            other_days = request.form.getlist('other_days')
            reason = request.form.get('reason')

            # Validate inputs
            if not my_days or not other_days or not swap_with_user_id:
                flash('يرجى ملء جميع الحقول المطلوبة', 'danger')
                return render_template('direct_shift_swap.html', user=user,
                                      employees=employees, schedule_status=schedule_status,
                                      month_name=month_name, current_year=year, num_days=num_days, day_of_week=day_of_week)

            if len(my_days) != len(other_days):
                flash('يجب أن يكون عدد الأيام المختارة متساوياً في كلا الجانبين', 'danger')
                return render_template('direct_shift_swap.html', user=user,
                                      employees=employees, schedule_status=schedule_status,
                                      month_name=month_name, current_year=year, num_days=num_days, day_of_week=day_of_week)

            # التحقق من عدم وجود تداخل في الأيام
            common_days = set(my_days) & set(other_days)
            if common_days:
                flash('لا يمكن أن تكون نفس الأيام في كلا الجانبين', 'danger')
                return render_template('direct_shift_swap.html', user=user,
                                      employees=employees, schedule_status=schedule_status,
                                      month_name=month_name, current_year=year, num_days=num_days, day_of_week=day_of_week)

            # Process multiple day swap
            db = get_db()
            for i, my_day in enumerate(my_days):
                other_day = other_days[i]

                # إنشاء طلب تبديل لكل يوم
                db.execute('''
                    INSERT INTO shift_swap_requests
                    (user_id, swap_type, swap_reason_type, swap_duration, swap_with_user_id,
                     swap_date, reason, status, manager_approval, hr_approval, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', [user['id'], 'day_swap_direct', 'regular_shift', 'one_day', swap_with_user_id,
                      f"{year}-{month:02d}-{my_day:02d}", f"تبديل يوم {my_day} مع يوم {other_day} - {reason}", 'pending', 0, 0, datetime.now()])

            db.commit()
            flash(f'تم إرسال طلب تبديل {len(my_days)} أيام بنجاح', 'success')
            return redirect(url_for('my_shift_schedule'))

        elif swap_type == 'shift_swap':
            my_day = request.form.get('my_day_shift')
            other_day = request.form.get('other_day_shift')
            my_shift = request.form.get('my_shift')
            other_shift = request.form.get('other_shift')
            reason = request.form.get('reason')

            # Validate inputs
            if not my_day or not other_day or not my_shift or not other_shift or not swap_with_user_id:
                flash('يرجى ملء جميع الحقول المطلوبة', 'danger')
                return render_template('direct_shift_swap.html', user=user,
                                      employees=employees, schedule_status=schedule_status,
                                      month_name=month_name, current_year=year, num_days=num_days, day_of_week=day_of_week)

            if my_day == other_day and my_shift == other_shift:
                flash('لا يمكن تبديل نفس الشفت في نفس اليوم', 'danger')
                return render_template('direct_shift_swap.html', user=user,
                                      employees=employees, schedule_status=schedule_status,
                                      month_name=month_name, current_year=year, num_days=num_days, day_of_week=day_of_week)

            # Process shift swap
            db = get_db()
            db.execute('''
                INSERT INTO shift_swap_requests
                (user_id, swap_type, swap_reason_type, swap_duration, swap_with_user_id,
                 swap_date, reason, status, manager_approval, hr_approval, created_at, shift_type, requester_shift_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', [user['id'], 'shift_swap_direct', 'regular_shift', 'one_day', swap_with_user_id,
                  f"{year}-{month:02d}-{my_day:02d}", f"تبديل شفت {my_shift} يوم {my_day} مع شفت {other_shift} يوم {other_day} - {reason}", 'pending', 0, 0, datetime.now(), other_shift, my_shift])

            db.commit()
            flash('تم إرسال طلب تبديل الشفت بنجاح', 'success')
            return redirect(url_for('my_shift_schedule'))

        elif swap_type == 'multi_shift_swap':
            my_multi_days = request.form.getlist('my_multi_days[]')
            my_multi_shifts = request.form.getlist('my_multi_shifts[]')
            other_multi_days = request.form.getlist('other_multi_days[]')
            other_multi_shifts = request.form.getlist('other_multi_shifts[]')
            reason = request.form.get('reason')

            # تصفية القيم الفارغة
            my_multi_days = [day for day in my_multi_days if day]
            my_multi_shifts = [shift for shift in my_multi_shifts if shift]
            other_multi_days = [day for day in other_multi_days if day]
            other_multi_shifts = [shift for shift in other_multi_shifts if shift]

            # Validate inputs
            if not my_multi_days or not my_multi_shifts or not other_multi_days or not other_multi_shifts:
                flash('يرجى ملء جميع الحقول المطلوبة', 'danger')
                return render_template('direct_shift_swap.html', user=user,
                                      employees=employees, schedule_status=schedule_status,
                                      month_name=month_name, current_year=year, num_days=num_days, day_of_week=day_of_week)

            if len(my_multi_days) != len(my_multi_shifts) or len(other_multi_days) != len(other_multi_shifts):
                flash('يجب اختيار يوم وشفت لكل صف', 'danger')
                return render_template('direct_shift_swap.html', user=user,
                                      employees=employees, schedule_status=schedule_status,
                                      month_name=month_name, current_year=year, num_days=num_days, day_of_week=day_of_week)

            if len(my_multi_days) != len(other_multi_days):
                flash('يجب أن يكون عدد الأيام والشفتات متساوياً في كلا الجانبين', 'danger')
                return render_template('direct_shift_swap.html', user=user,
                                      employees=employees, schedule_status=schedule_status,
                                      month_name=month_name, current_year=year, num_days=num_days, day_of_week=day_of_week)

            # Process multiple shift swap
            db = get_db()
            for i, my_day in enumerate(my_multi_days):
                my_shift = my_multi_shifts[i]
                other_day = other_multi_days[i]
                other_shift = other_multi_shifts[i]

                # إنشاء طلب تبديل لكل يوم وشفت
                db.execute('''
                    INSERT INTO shift_swap_requests
                    (user_id, swap_type, swap_reason_type, swap_duration, swap_with_user_id,
                     swap_date, reason, status, manager_approval, hr_approval, created_at, shift_type, requester_shift_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', [user['id'], 'multi_shift_swap_direct', 'regular_shift', 'one_day', swap_with_user_id,
                      f"{year}-{month:02d}-{my_day:02d}", f"تبديل شفت {my_shift} يوم {my_day} مع شفت {other_shift} يوم {other_day} - {reason}", 'pending', 0, 0, datetime.now(), other_shift, my_shift])

            db.commit()
            flash(f'تم إرسال طلب تبديل {len(my_multi_days)} شفتات بنجاح', 'success')
            return redirect(url_for('my_shift_schedule'))

    return render_template('direct_shift_swap.html', user=user,
                          employees=employees, schedule_status=schedule_status,
                          num_days=num_days, day_of_week=day_of_week, month_name=month_name,
                          current_year=year)





@app.route('/approve_direct_swap', methods=['POST'])
def approve_direct_swap():
    if not is_authenticated():
        return jsonify({'success': False, 'error': 'يجب تسجيل الدخول للموافقة على طلب التبديل'})

    user = get_current_user()

    # Check if user has permission to approve swap requests
    if user['role'] not in ['manager', 'admin', 'hr', 'gm']:
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية للموافقة على طلبات التبديل'})

    data = request.json
    request_id = data.get('request_id')

    # Get swap request
    swap_request = query_db('SELECT * FROM shift_swap_requests WHERE id = ?', [request_id], one=True)
    if not swap_request:
        return jsonify({'success': False, 'error': 'طلب التبديل غير موجود'})

    # Update swap request status
    db = get_db()
    db.execute('UPDATE shift_swap_requests SET status = "approved", manager_approval = 1 WHERE id = ?', [request_id])
    db.commit()

    return jsonify({'success': True})

@app.route('/reject_direct_swap', methods=['POST'])
def reject_direct_swap():
    if not is_authenticated():
        return jsonify({'success': False, 'error': 'يجب تسجيل الدخول لرفض طلب التبديل'})

    user = get_current_user()

    # Check if user has permission to reject swap requests
    if user['role'] not in ['manager', 'admin', 'hr', 'gm']:
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية لرفض طلبات التبديل'})

    data = request.json
    request_id = data.get('request_id')

    # Get swap request
    swap_request = query_db('SELECT * FROM shift_swap_requests WHERE id = ?', [request_id], one=True)
    if not swap_request:
        return jsonify({'success': False, 'error': 'طلب التبديل غير موجود'})

    # Update swap request status
    db = get_db()
    db.execute('UPDATE shift_swap_requests SET status = "rejected", manager_approval = -1 WHERE id = ?', [request_id])
    db.commit()

    return jsonify({'success': True})

@app.route('/shift_schedule')
def shift_schedule():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض جدول الشفتات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view shift schedules
    if user['role'] not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get current month and year
    current_month = datetime.now().month
    current_year = datetime.now().year

    # Get month from query parameters
    month = int(request.args.get('month', current_month))
    year = int(request.args.get('year', current_year))

    # Get number of days in the month
    num_days = calendar.monthrange(year, month)[1]

    # Get day of week for each day of the month (0 = Monday, 6 = Sunday)
    day_of_week = []
    for day in range(1, num_days + 1):
        day_date = datetime(year, month, day)
        weekday = day_date.weekday()
        # Adjust for Sunday as first day of week (0 = Saturday, 1 = Sunday, ..., 6 = Friday)
        day_of_week.append((weekday + 1) % 7)

    # Get employees
    employees = query_db('''
        SELECT * FROM users
        WHERE role = 'employee' AND is_active = 1
        ORDER BY first_name, last_name
    ''')

    # Get shift schedule for the month
    schedule = query_db('''
        SELECT * FROM shift_schedules
        WHERE month = ? AND year = ?
    ''', [month, year], one=True)

    # Get schedule status
    schedule_status = 'draft'
    if schedule:
        schedule_status = schedule['status']

        # Get shift assignments
        assignments = query_db('''
            SELECT * FROM shift_assignments
            WHERE schedule_id = ?
        ''', [schedule['id']])

        # Create shift data
        shift_data = {}
        for employee in employees:
            shift_data[employee['id']] = {}

        for assignment in assignments:
            shift_data[assignment['user_id']][assignment['day']] = assignment['shift_type']
    else:
        shift_data = {}
        for employee in employees:
            shift_data[employee['id']] = {}

    # Get Friday dates for the month
    fridays = []
    friday_employees = {
        'morning': None,
        'evening': [],
        'night': None
    }

    for day in range(1, num_days + 1):
        if day_of_week[day-1] == 6:  # Friday
            friday_date = f"{year}-{month:02d}-{day:02d}"

            # Get coverage employees for this Friday
            friday_coverage = query_db('''
                SELECT * FROM coverage_days
                WHERE coverage_date = ?
            ''', [friday_date])

            friday_data = {
                'date': friday_date,
                'morning_employee_id': None,
                'evening_employee_id': None,
                'night_employee_id': None
            }

            # إذا لم يكن هناك تغطية محددة، قم بتعيين الموظفين تلقائيًا
            if not friday_coverage:
                # اختر موظف واحد للفترة الصباحية
                morning_employee = query_db('''
                    SELECT id FROM users
                    WHERE role = 'employee' AND is_active = 1
                    ORDER BY RANDOM()
                    LIMIT 1
                ''', one=True)

                if morning_employee:
                    friday_data['morning_employee_id'] = morning_employee['id']
                    friday_employees['morning'] = morning_employee['id']

                # اختر موظفين للفترة المسائية
                if friday_data['morning_employee_id']:
                    evening_employees = query_db('''
                        SELECT id FROM users
                        WHERE role = 'employee' AND is_active = 1 AND id != ?
                        ORDER BY RANDOM()
                        LIMIT 2
                    ''', [friday_data['morning_employee_id']])
                else:
                    evening_employees = query_db('''
                        SELECT id FROM users
                        WHERE role = 'employee' AND is_active = 1
                        ORDER BY RANDOM()
                        LIMIT 2
                    ''')

                if evening_employees:
                    friday_data['evening_employee_id'] = [emp['id'] for emp in evening_employees]
                    friday_employees['evening'] = [emp['id'] for emp in evening_employees]

                # اختر موظف واحد للفترة الليلية
                excluded_ids = [friday_data['morning_employee_id']] if friday_data['morning_employee_id'] else []
                if friday_data['evening_employee_id']:
                    excluded_ids.extend(friday_data['evening_employee_id'])

                if excluded_ids:
                    placeholders = ','.join(['?' for _ in excluded_ids])
                    night_employee = query_db(f'''
                        SELECT id FROM users
                        WHERE role = 'employee' AND is_active = 1
                        AND id NOT IN ({placeholders})
                        ORDER BY RANDOM()
                        LIMIT 1
                    ''', excluded_ids, one=True)
                else:
                    night_employee = query_db('''
                        SELECT id FROM users
                        WHERE role = 'employee' AND is_active = 1
                        ORDER BY RANDOM()
                        LIMIT 1
                    ''', one=True)

                if night_employee:
                    friday_data['night_employee_id'] = night_employee['id']
                    friday_employees['night'] = night_employee['id']

                # أضف تعيينات الشفت ليوم الجمعة
                if schedule and schedule_status == 'draft':
                    db = get_db()

                    # حذف أي تعيينات سابقة ليوم الجمعة
                    db.execute('''
                        DELETE FROM shift_assignments
                        WHERE schedule_id = ? AND day = ?
                    ''', [schedule['id'], day])

                    # إضافة تعيين للفترة الصباحية
                    if friday_data['morning_employee_id']:
                        db.execute('''
                            INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                            VALUES (?, ?, ?, ?)
                        ''', [schedule['id'], friday_data['morning_employee_id'], day, 'morning'])

                    # إضافة تعيينات للفترة المسائية
                    if friday_data['evening_employee_id']:
                        for emp_id in friday_data['evening_employee_id']:
                            db.execute('''
                                INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                                VALUES (?, ?, ?, ?)
                            ''', [schedule['id'], emp_id, day, 'evening'])

                    # إضافة تعيين للفترة الليلية
                    if friday_data['night_employee_id']:
                        db.execute('''
                            INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                            VALUES (?, ?, ?, ?)
                        ''', [schedule['id'], friday_data['night_employee_id'], day, 'night'])

                    db.commit()
            else:
                # استخدم التغطية المحددة مسبقًا
                for coverage in friday_coverage:
                    if coverage['shift_type'] == 'morning':
                        friday_data['morning_employee_id'] = coverage['user_id']
                        friday_employees['morning'] = coverage['user_id']
                    elif coverage['shift_type'] == 'evening':
                        if not friday_data['evening_employee_id']:
                            friday_data['evening_employee_id'] = []
                        friday_data['evening_employee_id'].append(coverage['user_id'])
                        friday_employees['evening'].append(coverage['user_id'])
                    elif coverage['shift_type'] == 'night':
                        friday_data['night_employee_id'] = coverage['user_id']
                        friday_employees['night'] = coverage['user_id']

            fridays.append(friday_data)

    # Get month name
    month_names = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
    month_name = month_names[month - 1]

    return render_template('shift_schedule.html', user=user, year=year, month=month,
                          current_month=month, current_year=year, month_name=month_name,
                          num_days=num_days, day_of_week=day_of_week, employees=employees,
                          shift_data=shift_data, schedule_status=schedule_status, fridays=fridays,
                          friday_employees=friday_employees)

@app.route('/generate_shift_schedule', methods=['POST'])
def generate_shift_schedule():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لإنشاء جدول الشفتات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to generate shift schedules
    if user['role'] not in ['admin', 'manager']:
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية لإنشاء جدول الشفتات'})

    data = request.json
    month = int(data.get('month'))
    year = int(data.get('year'))

    # Check if schedule already exists
    existing_schedule = query_db('''
        SELECT * FROM shift_schedules
        WHERE month = ? AND year = ?
    ''', [month, year], one=True)

    db = get_db()

    if existing_schedule:
        # Delete existing schedule and assignments
        db.execute('DELETE FROM shift_assignments WHERE schedule_id = ?', [existing_schedule['id']])
        db.execute('DELETE FROM shift_schedules WHERE id = ?', [existing_schedule['id']])
        db.commit()

    # Create new schedule
    db.execute('''
        INSERT INTO shift_schedules (month, year, status, created_by, created_at)
        VALUES (?, ?, ?, ?, ?)
    ''', [month, year, 'draft', user['id'], datetime.now()])
    db.commit()

    # Get the new schedule ID
    schedule = query_db('''
        SELECT * FROM shift_schedules
        WHERE month = ? AND year = ?
    ''', [month, year], one=True)

    # Get employees
    employees = query_db('''
        SELECT * FROM users
        WHERE role = 'employee' AND is_active = 1
        ORDER BY first_name, last_name
    ''')

    # Get number of days in the month
    num_days = calendar.monthrange(year, month)[1]

    # Create shift assignments
    # We'll assign 4 employees to morning shift, 5 to evening, 2 to night, and 2 to split shift
    # This is a simple assignment - in a real system, you'd have more complex logic

    morning_employees = employees[:4]
    evening_employees = employees[4:9]
    night_employees = employees[9:11]
    split_employees = employees[11:13]

    # Assign shifts for each day except Friday
    for day in range(1, num_days + 1):
        day_date = datetime(year, month, day)
        weekday = day_date.weekday()
        # Adjust for Sunday as first day of week (0 = Saturday, 1 = Sunday, ..., 6 = Friday)
        adjusted_weekday = (weekday + 1) % 7

        if adjusted_weekday != 6:  # Not Friday
            # Assign morning shifts
            for emp in morning_employees:
                db.execute('''
                    INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                    VALUES (?, ?, ?, ?)
                ''', [schedule['id'], emp['id'], day, 'morning'])

            # Assign evening shifts
            for emp in evening_employees:
                db.execute('''
                    INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                    VALUES (?, ?, ?, ?)
                ''', [schedule['id'], emp['id'], day, 'evening'])

            # Assign night shifts
            for emp in night_employees:
                db.execute('''
                    INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                    VALUES (?, ?, ?, ?)
                ''', [schedule['id'], emp['id'], day, 'night'])

            # Assign split shifts
            for emp in split_employees:
                db.execute('''
                    INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                    VALUES (?, ?, ?, ?)
                ''', [schedule['id'], emp['id'], day, 'split'])
        else:  # Friday - يوم الجمعة
            # اختر موظف واحد للفترة الصباحية
            morning_employee = employees[0] if employees else None
            if morning_employee:
                db.execute('''
                    INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                    VALUES (?, ?, ?, ?)
                ''', [schedule['id'], morning_employee['id'], day, 'morning'])

            # اختر موظفين للفترة المسائية
            evening_employees_friday = employees[1:3] if len(employees) >= 3 else []
            for emp in evening_employees_friday:
                db.execute('''
                    INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                    VALUES (?, ?, ?, ?)
                ''', [schedule['id'], emp['id'], day, 'evening'])

            # اختر موظف واحد للفترة الليلية
            night_employee = employees[3] if len(employees) >= 4 else None
            if night_employee:
                db.execute('''
                    INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                    VALUES (?, ?, ?, ?)
                ''', [schedule['id'], night_employee['id'], day, 'night'])

    db.commit()

    return jsonify({'success': True})

@app.route('/update_shift', methods=['POST'])
def update_shift():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتعديل الشفت', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to update shifts
    if user['role'] != 'manager':
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية لتعديل الشفتات'})

    data = request.json
    user_id = int(data.get('user_id'))
    day = int(data.get('day'))
    shift = data.get('shift')
    month = int(data.get('month'))
    year = int(data.get('year'))

    # Get schedule
    schedule = query_db('''
        SELECT * FROM shift_schedules
        WHERE month = ? AND year = ?
    ''', [month, year], one=True)

    if not schedule:
        return jsonify({'success': False, 'error': 'الجدول غير موجود'})

    if schedule['status'] != 'draft':
        return jsonify({'success': False, 'error': 'لا يمكن تعديل جدول معتمد'})

    db = get_db()

    # Check if assignment exists
    assignment = query_db('''
        SELECT * FROM shift_assignments
        WHERE schedule_id = ? AND user_id = ? AND day = ?
    ''', [schedule['id'], user_id, day], one=True)

    if assignment:
        if shift == 'none':
            # Delete assignment
            db.execute('''
                DELETE FROM shift_assignments
                WHERE schedule_id = ? AND user_id = ? AND day = ?
            ''', [schedule['id'], user_id, day])
        else:
            # Update assignment
            db.execute('''
                UPDATE shift_assignments
                SET shift_type = ?
                WHERE schedule_id = ? AND user_id = ? AND day = ?
            ''', [shift, schedule['id'], user_id, day])
    else:
        if shift != 'none':
            # Create new assignment
            db.execute('''
                INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                VALUES (?, ?, ?, ?)
            ''', [schedule['id'], user_id, day, shift])

    db.commit()

    return jsonify({'success': True})

@app.route('/select_friday_shift', methods=['POST'])
def select_friday_shift():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتعديل دوام يوم الجمعة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    data = request.json
    day = int(data.get('day'))
    shift = data.get('shift')
    month = int(data.get('month'))
    year = int(data.get('year'))

    # Get schedule
    schedule = query_db('''
        SELECT * FROM shift_schedules
        WHERE month = ? AND year = ?
    ''', [month, year], one=True)

    if not schedule:
        return jsonify({'success': False, 'error': 'الجدول غير موجود'})

    if schedule['status'] != 'draft':
        return jsonify({'success': False, 'error': 'لا يمكن تعديل جدول معتمد'})

    # Check if the day is a Friday
    day_date = datetime(year, month, day)
    weekday = day_date.weekday()
    # Adjust for Sunday as first day of week (0 = Saturday, 1 = Sunday, ..., 6 = Friday)
    adjusted_weekday = (weekday + 1) % 7

    if adjusted_weekday != 6:  # Not a Friday
        return jsonify({'success': False, 'error': 'يمكن تعديل أيام الجمعة فقط'})

    db = get_db()

    # Check if assignment exists
    assignment = query_db('''
        SELECT * FROM shift_assignments
        WHERE schedule_id = ? AND user_id = ? AND day = ?
    ''', [schedule['id'], user['id'], day], one=True)

    if assignment:
        if shift == 'none':
            # Delete assignment
            db.execute('''
                DELETE FROM shift_assignments
                WHERE schedule_id = ? AND user_id = ? AND day = ?
            ''', [schedule['id'], user['id'], day])
        else:
            # Update assignment
            db.execute('''
                UPDATE shift_assignments
                SET shift_type = ?
                WHERE schedule_id = ? AND user_id = ? AND day = ?
            ''', [shift, schedule['id'], user['id'], day])
    else:
        if shift != 'none':
            # Create new assignment
            db.execute('''
                INSERT INTO shift_assignments (schedule_id, user_id, day, shift_type)
                VALUES (?, ?, ?, ?)
            ''', [schedule['id'], user['id'], day, shift])

    db.commit()

    return jsonify({'success': True})

@app.route('/update_friday_coverage', methods=['POST'])
def update_friday_coverage():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتعديل تغطية يوم الجمعة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to update Friday coverage
    if user['role'] != 'manager':
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية لتعديل تغطية يوم الجمعة'})

    data = request.json
    date = data.get('date')
    shift = data.get('shift')
    employee_id = data.get('employee_id')

    db = get_db()

    # Check if coverage exists
    coverage = query_db('''
        SELECT * FROM coverage_days
        WHERE coverage_date = ? AND shift_type = ?
    ''', [date, shift], one=True)

    if coverage:
        if employee_id:
            # Update coverage
            db.execute('''
                UPDATE coverage_days
                SET user_id = ?
                WHERE coverage_date = ? AND shift_type = ?
            ''', [employee_id, date, shift])
        else:
            # Delete coverage
            db.execute('''
                DELETE FROM coverage_days
                WHERE coverage_date = ? AND shift_type = ?
            ''', [date, shift])
    else:
        if employee_id:
            # Create new coverage
            db.execute('''
                INSERT INTO coverage_days (user_id, coverage_date, shift_type, status, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', [employee_id, date, shift, 'pending', datetime.now()])

    db.commit()

    return jsonify({'success': True})

@app.route('/approve_shift_schedule', methods=['POST'])
def approve_shift_schedule():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للموافقة على جدول الشفتات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    data = request.json
    month = int(data.get('month'))
    year = int(data.get('year'))
    approval_type = data.get('approval_type')

    # Check if user has permission to approve
    if (approval_type == 'manager' and user['role'] != 'manager') or \
       (approval_type == 'hr' and user['role'] != 'hr') or \
       (approval_type == 'gm' and user['role'] != 'gm'):
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية للموافقة على الجدول'})

    # Get schedule
    schedule = query_db('''
        SELECT * FROM shift_schedules
        WHERE month = ? AND year = ?
    ''', [month, year], one=True)

    if not schedule:
        return jsonify({'success': False, 'error': 'الجدول غير موجود'})

    # Check if schedule can be approved
    if approval_type == 'manager' and schedule['status'] != 'draft':
        return jsonify({'success': False, 'error': 'الجدول ليس في حالة مسودة'})
    elif approval_type == 'hr' and schedule['status'] != 'manager_approved':
        return jsonify({'success': False, 'error': 'الجدول لم تتم الموافقة عليه من قبل المدير'})
    elif approval_type == 'gm' and schedule['status'] != 'hr_approved':
        return jsonify({'success': False, 'error': 'الجدول لم تتم الموافقة عليه من قبل الموارد البشرية'})

    # Update schedule status
    db = get_db()

    if approval_type == 'manager':
        db.execute('''
            UPDATE shift_schedules
            SET status = 'manager_approved'
            WHERE id = ?
        ''', [schedule['id']])
    elif approval_type == 'hr':
        db.execute('''
            UPDATE shift_schedules
            SET status = 'hr_approved'
            WHERE id = ?
        ''', [schedule['id']])
    elif approval_type == 'gm':
        db.execute('''
            UPDATE shift_schedules
            SET status = 'gm_approved'
            WHERE id = ?
        ''', [schedule['id']])

        # When GM approves, update all Friday coverage days to approved
        friday_dates = []
        for day in range(1, calendar.monthrange(year, month)[1] + 1):
            day_date = datetime(year, month, day)
            if day_date.weekday() == 4:  # Friday (0 = Monday, 4 = Friday)
                friday_dates.append(day_date.strftime('%Y-%m-%d'))

        for date in friday_dates:
            db.execute('''
                UPDATE coverage_days
                SET status = 'approved'
                WHERE coverage_date = ?
            ''', [date])

    db.commit()

    return jsonify({'success': True})

@app.route('/approve_friday_request', methods=['POST'])
def approve_friday_request():
    if not is_authenticated():
        return jsonify({'success': False, 'error': 'يجب تسجيل الدخول للموافقة على طلب التغطية'})

    user = get_current_user()

    # Check if user has permission to approve Friday requests
    if user['role'] not in ['manager', 'admin']:
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية للموافقة على طلبات تغطية يوم الجمعة'})

    data = request.json
    request_id = data.get('request_id')
    action = data.get('action')

    # Get Friday coverage request
    friday_request = query_db('SELECT * FROM coverage_requests WHERE id = ?', [request_id], one=True)
    if not friday_request:
        return jsonify({'success': False, 'error': 'طلب التغطية غير موجود'})

    # Update Friday request
    db = get_db()
    if action == 'approve':
        db.execute('UPDATE coverage_requests SET manager_approval = 1, status = "approved" WHERE id = ?', [request_id])

        # Add to coverage days
        db.execute('''
            INSERT INTO coverage_days
            (user_id, coverage_date, shift_type, status, created_at)
            VALUES (?, ?, ?, ?, ?)
        ''', [friday_request['user_id'], friday_request['coverage_date'],
              friday_request['coverage_type'], 'approved', datetime.now()])

        # Add coverage compensation leave balance
        add_coverage_compensation_day(friday_request['user_id'], friday_request['coverage_date'], friday_request['coverage_type'])
    else:  # reject
        db.execute('UPDATE coverage_requests SET manager_approval = -1, status = "rejected" WHERE id = ?', [request_id])

    db.commit()
    return jsonify({'success': True})



@app.route('/monthly_operations')
def monthly_operations():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض العمليات الشهرية', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view monthly operations
    if user['role'] != 'gm':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    # Get current month and year
    current_month = datetime.now().month
    current_year = datetime.now().year

    # Get month from query parameters
    month = int(request.args.get('month', current_month))
    year = int(request.args.get('year', current_year))

    # Get operations for the month
    operations = query_db('''
        SELECT * FROM monthly_operations
        WHERE month = ? AND year = ?
        ORDER BY operation_date
    ''', [month, year])

    # Get month name
    month_names = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']
    month_name = month_names[month - 1]

    return render_template('monthly_operations.html', user=user, operations=operations,
                          month=month, year=year, current_month=month, current_year=year,
                          month_name=month_name)

@app.route('/add_monthly_operation', methods=['POST'])
def add_monthly_operation():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لإضافة عملية شهرية', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to add monthly operations
    if user['role'] != 'gm':
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية لإضافة عمليات شهرية'})

    data = request.json
    operation_date = data.get('operation_date')
    description = data.get('description')
    month = int(data.get('month'))
    year = int(data.get('year'))

    # Validate date
    try:
        date_obj = datetime.strptime(operation_date, '%Y-%m-%d').date()
        operation_month = date_obj.month
        operation_year = date_obj.year

        if operation_month != month or operation_year != year:
            return jsonify({'success': False, 'error': 'التاريخ يجب أن يكون في الشهر والسنة المحددين'})
    except ValueError:
        return jsonify({'success': False, 'error': 'صيغة التاريخ غير صحيحة'})

    # Add operation
    db = get_db()
    db.execute('''
        INSERT INTO monthly_operations (month, year, operation_date, description, created_by, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', [month, year, operation_date, description, user['id'], datetime.now()])
    db.commit()

    return jsonify({'success': True})

@app.route('/update_monthly_operation', methods=['POST'])
def update_monthly_operation():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتعديل عملية شهرية', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to update monthly operations
    if user['role'] != 'gm':
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية لتعديل عمليات شهرية'})

    data = request.json
    operation_id = int(data.get('operation_id'))
    operation_date = data.get('operation_date')
    description = data.get('description')

    # Get operation
    operation = query_db('''
        SELECT * FROM monthly_operations
        WHERE id = ?
    ''', [operation_id], one=True)

    if not operation:
        return jsonify({'success': False, 'error': 'العملية غير موجودة'})

    # Validate date
    try:
        date_obj = datetime.strptime(operation_date, '%Y-%m-%d').date()
        operation_month = date_obj.month
        operation_year = date_obj.year

        if operation_month != operation['month'] or operation_year != operation['year']:
            return jsonify({'success': False, 'error': 'التاريخ يجب أن يكون في نفس الشهر والسنة'})
    except ValueError:
        return jsonify({'success': False, 'error': 'صيغة التاريخ غير صحيحة'})

    # Update operation
    db = get_db()
    db.execute('''
        UPDATE monthly_operations
        SET operation_date = ?, description = ?
        WHERE id = ?
    ''', [operation_date, description, operation_id])
    db.commit()

    return jsonify({'success': True})

@app.route('/delete_monthly_operation', methods=['POST'])
def delete_monthly_operation():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لحذف عملية شهرية', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to delete monthly operations
    if user['role'] != 'gm':
        return jsonify({'success': False, 'error': 'ليس لديك صلاحية لحذف عمليات شهرية'})

    data = request.json
    operation_id = int(data.get('operation_id'))

    # Delete operation
    db = get_db()
    db.execute('DELETE FROM monthly_operations WHERE id = ?', [operation_id])
    db.commit()

    return jsonify({'success': True})

@app.route('/settings')
def settings():
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى الإعدادات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view settings
    if user['role'] != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('settings.html', user=user)

@app.route('/all_notifications')
def all_notifications():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لعرض الإشعارات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Check if user has permission to view notifications
    if user['role'] not in ['admin', 'hr', 'manager', 'gm', 'hospital_manager']:
        flash('ليس لديك صلاحية لعرض الإشعارات', 'danger')
        return redirect(url_for('dashboard'))

    notifications = []

    # Get pending leave requests
    if user['role'] == 'manager':
        pending_leaves = query_db('''
            SELECT lr.*, lt.name as leave_type_name, u.first_name, u.last_name
            FROM leave_requests lr
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            JOIN users u ON lr.user_id = u.id
            WHERE lr.status = 'pending' AND u.department_id = ? AND lr.manager_approval = 0
            ORDER BY lr.created_at DESC
        ''', [user['department_id']])
    elif user['role'] == 'hr':
        pending_leaves = query_db('''
            SELECT lr.*, lt.name as leave_type_name, u.first_name, u.last_name, d.name as department_name
            FROM leave_requests lr
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            JOIN users u ON lr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE lr.status = 'pending' AND lr.manager_approval = 1 AND lr.hr_approval = 0
            ORDER BY lr.created_at DESC
        ''')
    elif user['role'] in ['hospital_manager', 'admin']:
        pending_leaves = query_db('''
            SELECT lr.*, lt.name as leave_type_name, u.first_name, u.last_name, d.name as department_name
            FROM leave_requests lr
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            JOIN users u ON lr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE lr.status = 'pending' AND lr.manager_approval = 1 AND lr.hr_approval = 1 AND lr.hospital_manager_approval = 0
            ORDER BY lr.created_at DESC
        ''')
    else:
        pending_leaves = []

    # Add leave notifications
    for leave in pending_leaves:
        notifications.append({
            'type': 'leave_request',
            'title': f'طلب إجازة {leave["leave_type_name"]}',
            'message': f'طلب من {leave["first_name"]} {leave["last_name"]}',
            'date': leave['created_at'],
            'url': url_for('leave_requests'),
            'icon': 'fas fa-calendar-alt',
            'color': 'primary'
        })

    # Get pending coverage requests
    if user['role'] == 'manager':
        pending_coverage = query_db('''
            SELECT cr.*, u.first_name, u.last_name
            FROM coverage_requests cr
            JOIN users u ON cr.user_id = u.id
            WHERE cr.status = 'pending' AND u.department_id = ?
            ORDER BY cr.created_at DESC
        ''', [user['department_id']])
    elif user['role'] in ['admin', 'hr', 'gm', 'hospital_manager']:
        pending_coverage = query_db('''
            SELECT cr.*, u.first_name, u.last_name, d.name as department_name
            FROM coverage_requests cr
            JOIN users u ON cr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE cr.status = 'pending'
            ORDER BY cr.created_at DESC
        ''')
    else:
        pending_coverage = []

    # Add coverage notifications
    for coverage in pending_coverage:
        notifications.append({
            'type': 'coverage_request',
            'title': 'طلب تغطية',
            'message': f'طلب من {coverage["first_name"]} {coverage["last_name"]} لتاريخ {coverage["coverage_date"]}',
            'date': coverage['created_at'],
            'url': url_for('coverage_requests'),
            'icon': 'fas fa-exchange-alt',
            'color': 'success'
        })

    # Get pending shift swap requests
    if user['role'] == 'manager':
        pending_swaps = query_db('''
            SELECT ssr.*, u.first_name, u.last_name
            FROM shift_swap_requests ssr
            JOIN users u ON ssr.user_id = u.id
            WHERE ssr.status = 'pending' AND u.department_id = ?
            ORDER BY ssr.created_at DESC
        ''', [user['department_id']])
    elif user['role'] in ['admin', 'hr', 'gm', 'hospital_manager']:
        pending_swaps = query_db('''
            SELECT ssr.*, u.first_name, u.last_name, d.name as department_name
            FROM shift_swap_requests ssr
            JOIN users u ON ssr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE ssr.status = 'pending'
            ORDER BY ssr.created_at DESC
        ''')
    else:
        pending_swaps = []

    # Add swap notifications
    for swap in pending_swaps:
        notifications.append({
            'type': 'swap_request',
            'title': 'طلب تبديل دوام',
            'message': f'طلب من {swap["first_name"]} {swap["last_name"]}',
            'date': swap['created_at'],
            'url': url_for('shift_swap_requests'),
            'icon': 'fas fa-sync-alt',
            'color': 'warning'
        })

    # Sort notifications by date (newest first)
    notifications.sort(key=lambda x: x['date'], reverse=True)

    return render_template('all_notifications.html', user=user, notifications=notifications)

@app.route('/export_requests')
def export_requests():
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتصدير الطلبات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get filter parameters
    request_type = request.args.get('request_type', 'all')
    status = request.args.get('status', 'approved')  # Default to approved requests
    date_range = request.args.get('date_range', 'all')

    # Build date filter based on date_range
    date_filter = ''

    if date_range == 'today':
        date_filter = "AND DATE(created_at) = DATE('now')"
    elif date_range == 'week':
        date_filter = "AND DATE(created_at) >= DATE('now', '-7 days')"
    elif date_range == 'month':
        date_filter = "AND DATE(created_at) >= DATE('now', '-1 month')"
    elif date_range == 'year':
        date_filter = "AND DATE(created_at) >= DATE('now', '-1 year')"

    # Get requests based on filters
    requests = []

    # For regular employees, show only their own requests
    if user['role'] == 'employee':
        user_filter = f"AND user_id = {user['id']}"
    else:
        user_filter = ""

    # Status filter
    if status != 'all':
        status_filter = f"AND status = '{status}'"
    else:
        status_filter = ""

    # Get leave requests
    if request_type in ['all', 'leave']:
        leave_requests = query_db(f'''
            SELECT lr.*, lt.name as leave_type_name, 'leave' as request_type
            FROM leave_requests lr
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            WHERE 1=1 {user_filter} {status_filter} {date_filter}
            ORDER BY lr.created_at DESC
        ''')
        requests.extend(leave_requests)

    # Get coverage requests
    if request_type in ['all', 'coverage']:
        coverage_requests = query_db(f'''
            SELECT cr.*,
                   CASE
                       WHEN cr.coverage_type = 'morning' THEN 'دوام صباحي'
                       WHEN cr.coverage_type = 'evening' THEN 'دوام مسائي'
                       WHEN cr.coverage_type = 'night' THEN 'دوام ليلي'
                   END as coverage_type_name,
                   'coverage' as request_type
            FROM coverage_requests cr
            WHERE 1=1 {user_filter} {status_filter} {date_filter}
            ORDER BY cr.created_at DESC
        ''')
        requests.extend(coverage_requests)

    # Get shift swap requests
    if request_type in ['all', 'shift_swap']:
        shift_swap_requests = query_db(f'''
            SELECT ssr.*, 'shift_swap' as request_type
            FROM shift_swap_requests ssr
            WHERE 1=1 {user_filter} {status_filter} {date_filter}
            ORDER BY ssr.created_at DESC
        ''')
        requests.extend(shift_swap_requests)

    # Get profile change requests
    if request_type in ['all', 'profile']:
        profile_requests = query_db(f'''
            SELECT pcr.*, 'profile' as request_type
            FROM profile_change_requests pcr
            WHERE 1=1 {user_filter} {status_filter} {date_filter}
            ORDER BY pcr.created_at DESC
        ''')
        requests.extend(profile_requests)

    return render_template('export_requests.html', user=user,
                          requests=requests, request_type=request_type,
                          status=status, date_range=date_range)

@app.route('/export_request_pdf/<string:request_type>/<int:request_id>')
def export_request_pdf(request_type, request_id):
    if not is_authenticated():
        flash('يجب تسجيل الدخول لتصدير الطلبات', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # Get request data based on type
    request_data = None
    request_type_name = ""
    employee_name = ""
    department_name = ""
    manager_name = ""
    hr_name = ""
    swap_with_name = ""
    leave_days = 0

    if request_type == 'leave':
        request_data = query_db('''
            SELECT lr.*, lt.name as leave_type_name, u.first_name, u.last_name, d.name as department_name
            FROM leave_requests lr
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            JOIN users u ON lr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE lr.id = ?
        ''', [request_id], one=True)

        if request_data:
            request_type_name = "طلب إجازة"
            employee_name = f"{request_data['first_name']} {request_data['last_name']}"
            department_name = request_data['department_name']

            # Calculate leave days
            start_date = datetime.strptime(request_data['start_date'], '%Y-%m-%d').date()
            end_date = datetime.strptime(request_data['end_date'], '%Y-%m-%d').date()
            leave_days = (end_date - start_date).days + 1

    elif request_type == 'coverage':
        request_data = query_db('''
            SELECT cr.*, u.first_name, u.last_name, d.name as department_name,
                   CASE
                       WHEN cr.coverage_type = 'morning' THEN 'دوام صباحي'
                       WHEN cr.coverage_type = 'evening' THEN 'دوام مسائي'
                       WHEN cr.coverage_type = 'night' THEN 'دوام ليلي'
                   END as coverage_type_name
            FROM coverage_requests cr
            JOIN users u ON cr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE cr.id = ?
        ''', [request_id], one=True)

        if request_data:
            request_type_name = "طلب تغطية"
            employee_name = f"{request_data['first_name']} {request_data['last_name']}"
            department_name = request_data['department_name']

    elif request_type == 'shift_swap':
        request_data = query_db('''
            SELECT ssr.*, u1.first_name as requester_first_name, u1.last_name as requester_last_name,
                   u2.first_name as target_first_name, u2.last_name as target_last_name,
                   d.name as department_name
            FROM shift_swap_requests ssr
            JOIN users u1 ON ssr.user_id = u1.id
            LEFT JOIN users u2 ON ssr.swap_with_user_id = u2.id
            JOIN departments d ON u1.department_id = d.id
            WHERE ssr.id = ?
        ''', [request_id], one=True)

        if request_data:
            request_type_name = "طلب تبديل دوام"
            employee_name = f"{request_data['requester_first_name']} {request_data['requester_last_name']}"
            department_name = request_data['department_name']
            if request_data['swap_with_user_id']:
                swap_with_name = f"{request_data['target_first_name']} {request_data['target_last_name']}"

    elif request_type == 'profile':
        request_data = query_db('''
            SELECT pcr.*, u.first_name, u.last_name, d.name as department_name
            FROM profile_change_requests pcr
            JOIN users u ON pcr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE pcr.id = ?
        ''', [request_id], one=True)

        if request_data:
            request_type_name = "طلب تعديل ملف شخصي"
            employee_name = f"{request_data['first_name']} {request_data['last_name']}"
            department_name = request_data['department_name']

    if not request_data:
        flash('الطلب غير موجود', 'danger')
        return redirect(url_for('export_requests'))

    # Get manager and HR names
    manager = query_db('''
        SELECT first_name, last_name FROM users WHERE role = 'manager' AND department_id = ?
    ''', [request_data['department_id'] if 'department_id' in request_data else 1], one=True)

    hr = query_db('''
        SELECT first_name, last_name FROM users WHERE role = 'hr' LIMIT 1
    ''', one=True)

    if manager:
        manager_name = f"{manager['first_name']} {manager['last_name']}"

    if hr:
        hr_name = f"{hr['first_name']} {hr['last_name']}"

    # Set request title based on type
    if request_type == 'leave':
        request_title = f"طلب إجازة - {request_data['leave_type_name']}"
    elif request_type == 'coverage':
        request_title = "طلب تغطية دوام"
    elif request_type == 'shift_swap':
        request_title = "طلب تبديل دوام"
    elif request_type == 'profile':
        request_title = "طلب تعديل ملف شخصي"
    else:
        request_title = "طلب"

    # Get logo path (relative to static folder for web display)
    logo_path = url_for('static', filename='img/logo.svg')

    # Render template as printable HTML page
    return render_template('pdf_templates/request_pdf.html',
                          request=request_data,
                          request_type=request_type,
                          request_type_name=request_type_name,
                          request_title=request_title,
                          employee_name=employee_name,
                          department_name=department_name,
                          manager_name=manager_name,
                          hr_name=hr_name,
                          swap_with_name=swap_with_name,
                          leave_days=leave_days,
                          logo_path=logo_path,
                          print_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

if __name__ == '__main__':
    try:
        print("🚀 بدء تشغيل خادم ALEMEIS...")

        # تحقق من وجود قاعدة البيانات
        if not os.path.exists(app.config['DATABASE']):
            print("⚠️ قاعدة البيانات غير موجودة. جاري إنشاؤها...")
            init_db()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")

        print("📍 الخادم متاح على: http://localhost:5000")
        print("🔑 حسابات الاختبار:")
        print("   - المدير: admin / admin123")
        print("   - مدير المختبر: manager / admin123")
        print("   - الموارد البشرية: hr / admin123")
        print("   - المدير العام: gm / admin123")
        print("   - موظف 1: employee1 / admin123")
        print("   - موظف 2: employee2 / admin123")
        print("=" * 50)

        app.run(debug=True, host='0.0.0.0', port=5000)

    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        import traceback
        traceback.print_exc()
