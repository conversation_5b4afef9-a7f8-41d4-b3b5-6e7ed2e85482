{% extends "base.html" %}

{% block title %}ALEMIS - لوحة التحكم{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/medical-theme.css') }}">
<style>
    .dashboard-header {
        background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 8px 32px rgba(14, 165, 233, 0.3);
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -10%;
        width: 200px;
        height: 200px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 6s ease-in-out infinite;
    }

    .dashboard-header::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -5%;
        width: 150px;
        height: 150px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M19 8h-2v3h-3v2h3v3h2v-3h3v-2h-3V8zM4 8h2v8H4V8zm3 0h2v8H7V8zm3 0h2v8h-2V8z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 8s ease-in-out infinite reverse;
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        margin-bottom: 0.5rem;
    }

    .dashboard-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        font-weight: 300;
    }

    .dashboard-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: rgba(255,255,255,0.8);
    }

    .stats-card {
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        border: none;
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(14, 165, 233, 0.1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        position: relative;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--card-color, #0ea5e9), var(--card-color-light, #7dd3fc));
        z-index: 1;
    }

    .stats-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(14, 165, 233, 0.2);
    }

    .stats-card.primary { --card-color: #3b82f6; --card-color-light: #93c5fd; }
    .stats-card.warning { --card-color: #f59e0b; --card-color-light: #fbbf24; }
    .stats-card.success { --card-color: #10b981; --card-color-light: #34d399; }
    .stats-card.danger { --card-color: #ef4444; --card-color-light: #f87171; }
    .stats-card.info { --card-color: #06b6d4; --card-color-light: #22d3ee; }

    /* تحسين ألوان النصوص في البطاقات */
    .stats-card .stats-title,
    .stats-card .stats-number,
    .stats-card .stats-subtitle {
        color: #000 !important;
        font-weight: bold !important;
    }

    .stats-card .card-footer span,
    .stats-card .card-footer div {
        color: #000 !important;
    }

    /* تحسين ألوان الرسوم البيانية */
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
    }

    canvas {
        background: white !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Dashboard Header -->
<div class="dashboard-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="dashboard-icon">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
                <h1 class="dashboard-title">لوحة التحكم</h1>
                <p class="dashboard-subtitle">مرحباً {{ user.first_name }} {{ user.last_name }} - نظام إدارة المختبرات الطبية</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="dashboard-stats">
                    <div class="stat-item">
                        <i class="fas fa-user-md"></i>
                        <span>نظام إدارة متطور</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- User Info Card -->
    <div class="row">
        <div class="col-md-12">
            <div class="card medical-card medical-card-premium medical-glow mb-4 fade-in">
                <div class="card-header medical-card-header">
                    <div class="d-flex align-items-center">
                        <div class="header-icon medical-pulse">
                            <i class="fas fa-user-circle medical-icon-advanced"></i>
                        </div>
                        <div class="header-content">
                            <h5 class="mb-0">معلومات المستخدم</h5>
                            <small class="opacity-75">بيانات الحساب والصلاحيات</small>
                        </div>
                    </div>
                </div>
                <div class="card-body medical-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="user-info-item">
                                <i class="fas fa-user text-primary me-2"></i>
                                <strong>اسم المستخدم:</strong> {{ user.username }}
                            </div>
                            <div class="user-info-item">
                                <i class="fas fa-envelope text-info me-2"></i>
                                <strong>البريد الإلكتروني:</strong> {{ user.email }}
                            </div>
                            <div class="user-info-item">
                                <i class="fas fa-user-tag text-success me-2"></i>
                                <strong>الدور:</strong>
                                {% if user.role == 'admin' %}
                                    <span class="badge medical-badge-advanced" style="background: linear-gradient(135deg, #dc2626, #b91c1c);">مدير النظام</span>
                                {% elif user.role == 'hr' %}
                                    <span class="badge medical-badge-advanced" style="background: linear-gradient(135deg, #7c3aed, #6d28d9);">موارد بشرية</span>
                                {% elif user.role == 'manager' %}
                                    <span class="badge medical-badge-advanced" style="background: linear-gradient(135deg, #059669, #047857);">مدير قسم</span>
                                {% elif user.role == 'gm' %}
                                    <span class="badge medical-badge-advanced" style="background: linear-gradient(135deg, #d97706, #b45309);">مدير عام</span>
                                {% else %}
                                    <span class="badge medical-badge-advanced">موظف</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="user-info-item">
                                <i class="fas fa-building text-warning me-2"></i>
                                <strong>القسم:</strong>
                                {% set dept_id = user.department_id %}
                                {% if dept_id == 1 %}
                                    المختبر الكيميائي
                                {% elif dept_id == 2 %}
                                    المختبر البيولوجي
                                {% elif dept_id == 3 %}
                                    مختبر الأبحاث
                                {% elif dept_id == 4 %}
                                    الإدارة
                                {% elif dept_id == 5 %}
                                    الموارد البشرية
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </div>
                            <div class="user-info-item">
                                <i class="fas fa-calendar-plus text-secondary me-2"></i>
                                <strong>تاريخ الانضمام:</strong> {{ user.date_joined }}
                            </div>
                            <div class="user-info-item">
                                <i class="fas fa-circle text-success me-2"></i>
                                <strong>الحالة:</strong>
                                {% if user.is_active %}
                                    <span class="badge medical-status-badge status-approved">نشط</span>
                                {% else %}
                                    <span class="badge medical-status-badge status-rejected">غير نشط</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <a href="{{ url_for('leave_balances') }}" class="text-decoration-none">
                <div class="card stats-card primary text-white mb-4 medical-shadow-glow-hover fade-in">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="stats-icon">
                                <i class="fas fa-calendar-alt fa-3x medical-icon-advanced"></i>
                            </div>
                            <div class="text-end">
                                <h5 class="stats-title" style="color: #000 !important; font-weight: bold;">رصيد الإجازات</h5>
                                <h2 class="stats-number mb-0" style="color: #000 !important; font-weight: bold;">
                                    {% set total_balance = 0 %}
                                    {% for balance in leave_balances %}
                                        {% if balance.leave_type_name == 'إجازة اعتيادية' %}
                                            {{ balance.remaining_days }}
                                            {% set total_balance = balance.remaining_days %}
                                        {% endif %}
                                    {% endfor %}
                                    {% if total_balance == 0 %}
                                        0
                                    {% endif %}
                                </h2>
                                <small class="stats-subtitle" style="color: #333 !important;">يوم متبقي</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer stats-footer d-flex align-items-center justify-content-between">
                        <span class="small" style="color: #000 !important;">
                            <i class="fas fa-eye me-1"></i>عرض أرصدة الإجازات
                        </span>
                        <div class="small" style="color: #000 !important;"><i class="fas fa-arrow-left"></i></div>
                    </div>
                </div>
            </a>
        </div>

        <div class="col-xl-3 col-md-6">
            <a href="{{ url_for('my_leaves') }}?status=pending" class="text-decoration-none">
                <div class="card stats-card warning text-white mb-4 medical-shadow-glow-hover fade-in" style="animation-delay: 0.1s;">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="stats-icon">
                                <i class="fas fa-hourglass-half fa-3x medical-icon-advanced medical-pulse"></i>
                            </div>
                            <div class="text-end">
                                <h5 class="stats-title" style="color: #000 !important; font-weight: bold;">طلبات معلقة</h5>
                                <h2 class="stats-number mb-0" style="color: #000 !important; font-weight: bold;">
                                    {% set pending_count = 0 %}
                                    {% for leave in leave_requests %}
                                        {% if leave.status == 'pending' %}
                                            {% set pending_count = pending_count + 1 %}
                                        {% endif %}
                                    {% endfor %}
                                    {{ pending_count }}
                                </h2>
                                <small class="stats-subtitle" style="color: #333 !important;">في انتظار الموافقة</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer stats-footer d-flex align-items-center justify-content-between">
                        <span class="small" style="color: #000 !important;">
                            <i class="fas fa-eye me-1"></i>عرض الطلبات المعلقة
                        </span>
                        <div class="small" style="color: #000 !important;"><i class="fas fa-arrow-left"></i></div>
                    </div>
                </div>
            </a>
        </div>

        <div class="col-xl-3 col-md-6">
            <a href="{{ url_for('my_leaves') }}?status=approved" class="text-decoration-none">
                <div class="card stats-card success text-white mb-4 medical-shadow-glow-hover fade-in" style="animation-delay: 0.2s;">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="stats-icon">
                                <i class="fas fa-check-circle fa-3x medical-icon-advanced"></i>
                            </div>
                            <div class="text-end">
                                <h5 class="stats-title">طلبات موافق عليها</h5>
                                <h2 class="stats-number mb-0">
                                    {% set approved_count = 0 %}
                                    {% for leave in leave_requests %}
                                        {% if leave.status == 'approved' %}
                                            {% set approved_count = approved_count + 1 %}
                                        {% endif %}
                                    {% endfor %}
                                    {{ approved_count }}
                                </h2>
                                <small class="stats-subtitle">تم اعتمادها</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer stats-footer d-flex align-items-center justify-content-between">
                        <span class="small text-white">
                            <i class="fas fa-eye me-1"></i>عرض التفاصيل
                        </span>
                        <div class="small text-white"><i class="fas fa-arrow-left"></i></div>
                    </div>
                </div>
            </a>
        </div>

        <div class="col-xl-3 col-md-6">
            <a href="{{ url_for('my_leaves') }}?status=rejected" class="text-decoration-none">
                <div class="card stats-card danger text-white mb-4 medical-shadow-glow-hover fade-in" style="animation-delay: 0.3s;">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="stats-icon">
                                <i class="fas fa-times-circle fa-3x medical-icon-advanced"></i>
                            </div>
                            <div class="text-end">
                                <h5 class="stats-title">طلبات مرفوضة</h5>
                                <h2 class="stats-number mb-0">
                                    {% set rejected_count = 0 %}
                                    {% for leave in leave_requests %}
                                        {% if leave.status == 'rejected' %}
                                            {% set rejected_count = rejected_count + 1 %}
                                        {% endif %}
                                    {% endfor %}
                                    {{ rejected_count }}
                                </h2>
                                <small class="stats-subtitle">تم رفضها</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer stats-footer d-flex align-items-center justify-content-between">
                        <span class="small text-white">
                            <i class="fas fa-eye me-1"></i>عرض التفاصيل
                        </span>
                        <div class="small text-white"><i class="fas fa-arrow-left"></i></div>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <!-- Second Row of Stats -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <a href="{{ url_for('my_coverage') }}" class="text-decoration-none">
                <div class="card stats-card info text-white mb-4 medical-shadow-glow-hover fade-in" style="animation-delay: 0.4s;">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="stats-icon">
                                <i class="fas fa-calendar-plus fa-3x medical-icon-advanced"></i>
                            </div>
                            <div class="text-end">
                                <h5 class="stats-title" style="color: #000 !important; font-weight: bold;">رصيد بدل التغطية</h5>
                                <h2 class="stats-number mb-0" style="color: #000 !important; font-weight: bold;">
                                    {% set coverage_balance = 0 %}
                                    {% for balance in leave_balances %}
                                        {% if balance.leave_type_name == 'بدل يوم تغطية' %}
                                            {{ balance.remaining_days }}
                                            {% set coverage_balance = balance.remaining_days %}
                                        {% endif %}
                                    {% endfor %}
                                    {% if coverage_balance == 0 %}
                                        0
                                    {% endif %}
                                </h2>
                                <small class="stats-subtitle" style="color: #333 !important;">يوم تغطية متاح</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer stats-footer d-flex align-items-center justify-content-between">
                        <span class="small" style="color: #000 !important;">
                            <i class="fas fa-eye me-1"></i>عرض طلبات التغطية
                        </span>
                        <div class="small" style="color: #000 !important;"><i class="fas fa-arrow-left"></i></div>
                    </div>
                </div>
            </a>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card stats-card primary text-white mb-4 medical-shadow-glow-hover fade-in" style="animation-delay: 0.5s;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="stats-icon">
                            <i class="fas fa-user-md fa-3x medical-icon-advanced"></i>
                        </div>
                        <div class="text-end">
                            <h5 class="stats-title">حالة الحساب</h5>
                            <h2 class="stats-number mb-0">
                                {% if user.is_active %}
                                    نشط
                                {% else %}
                                    غير نشط
                                {% endif %}
                            </h2>
                            <small class="stats-subtitle">حالة المستخدم</small>
                        </div>
                    </div>
                </div>
                <div class="card-footer stats-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white text-decoration-none" href="{{ url_for('profile') }}">
                        <i class="fas fa-user me-1"></i>الملف الشخصي
                    </a>
                    <div class="small text-white"><i class="fas fa-arrow-left"></i></div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card stats-card success text-white mb-4 medical-shadow-glow-hover fade-in" style="animation-delay: 0.6s;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="stats-icon">
                            <i class="fas fa-calendar-week fa-3x medical-icon-advanced"></i>
                        </div>
                        <div class="text-end">
                            <h5 class="stats-title">جدول الدوام</h5>
                            <h2 class="stats-number mb-0">
                                {% if schedule_status == 'draft' %}
                                    مسودة
                                {% elif schedule_status == 'gm_approved' %}
                                    معتمد
                                {% else %}
                                    قيد المراجعة
                                {% endif %}
                            </h2>
                            <small class="stats-subtitle">حالة الجدول</small>
                        </div>
                    </div>
                </div>
                <div class="card-footer stats-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white text-decoration-none" href="{{ url_for('my_shift_schedule') }}">
                        <i class="fas fa-calendar me-1"></i>عرض الجدول
                    </a>
                    <div class="small text-white"><i class="fas fa-arrow-left"></i></div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <a href="{{ url_for('shift_swap_requests') }}" class="text-decoration-none">
                <div class="card stats-card warning text-white mb-4 medical-shadow-glow-hover fade-in" style="animation-delay: 0.7s;">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="stats-icon">
                                <i class="fas fa-exchange-alt fa-3x medical-icon-advanced"></i>
                            </div>
                            <div class="text-end">
                                <h5 class="stats-title">طلبات التبديل</h5>
                                <h2 class="stats-number mb-0">
                                    {% set swap_count = 0 %}
                                    {% if shift_swap_requests %}
                                        {% for swap in shift_swap_requests %}
                                            {% if swap.status == 'pending' %}
                                                {% set swap_count = swap_count + 1 %}
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                    {{ swap_count }}
                                </h2>
                                <small class="stats-subtitle">طلبات معلقة</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer stats-footer d-flex align-items-center justify-content-between">
                        <span class="small text-white">
                            <i class="fas fa-list me-1"></i>عرض الطلبات
                        </span>
                        <div class="small text-white"><i class="fas fa-arrow-left"></i></div>
                    </div>
                </div>
            </a>
        </div>
    </div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-calendar-week me-1"></i>
                    جدول دوامي لهذا الشهر
                </div>
                <div>
                    <a href="{{ url_for('my_shift_schedule') }}" class="btn btn-light btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>
                        عرض التفاصيل
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>حالة الجدول:</strong>
                    {% if schedule_status == 'draft' %}
                    <span class="badge bg-warning">مسودة</span>
                    <small class="d-block mt-1">يمكنك تبديل الدوام مع زملائك في هذه المرحلة</small>
                    {% elif schedule_status == 'manager_approved' %}
                    <span class="badge bg-info">موافقة المدير</span>
                    <small class="d-block mt-1">تم اعتماد الجدول من قبل المدير، يمكنك تقديم طلب تبديل رسمي</small>
                    {% elif schedule_status == 'hr_approved' %}
                    <span class="badge bg-primary">موافقة الموارد البشرية</span>
                    <small class="d-block mt-1">تم اعتماد الجدول من قبل الموارد البشرية، يمكنك تقديم طلب تبديل رسمي</small>
                    {% elif schedule_status == 'gm_approved' %}
                    <span class="badge bg-success">معتمد</span>
                    <small class="d-block mt-1">تم اعتماد الجدول بشكل نهائي، يمكنك تقديم طلب تبديل رسمي</small>
                    {% else %}
                    <span class="badge bg-secondary">غير متوفر</span>
                    {% endif %}
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                {% for day in range(1, 8) %}
                                <th class="text-center {% if day == 6 %}bg-light{% endif %} {% if day == 7 %}bg-light-gray{% endif %}">
                                    {% if day == 1 %}
                                    السبت
                                    {% elif day == 2 %}
                                    الأحد
                                    {% elif day == 3 %}
                                    الإثنين
                                    {% elif day == 4 %}
                                    الثلاثاء
                                    {% elif day == 5 %}
                                    الأربعاء
                                    {% elif day == 6 %}
                                    الخميس
                                    {% elif day == 7 %}
                                    الجمعة
                                    {% endif %}
                                </th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                {% for day in range(1, 8) %}
                                <td class="text-center {% if day == 6 %}bg-light{% endif %} {% if day == 7 %}bg-light-gray{% endif %}">
                                    {% if current_week_shifts and current_week_shifts[day] %}
                                    <span class="badge {% if current_week_shifts[day] == 'morning' %}bg-success{% elif current_week_shifts[day] == 'evening' %}bg-primary{% elif current_week_shifts[day] == 'night' %}bg-dark{% elif current_week_shifts[day] == 'split' %}bg-info{% endif %}">
                                        {% if current_week_shifts[day] == 'morning' %}
                                        ص
                                        {% elif current_week_shifts[day] == 'evening' %}
                                        م
                                        {% elif current_week_shifts[day] == 'night' %}
                                        ل
                                        {% elif current_week_shifts[day] == 'split' %}
                                        ف
                                        {% endif %}
                                    </span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mt-2">
                    <div class="d-flex justify-content-start flex-wrap">
                        <div class="me-3 mb-2"><span class="badge bg-success">ص</span> دوام صباحي (8ص-4م)</div>
                        <div class="me-3 mb-2"><span class="badge bg-primary">م</span> دوام مسائي (4م-12ل)</div>
                        <div class="me-3 mb-2"><span class="badge bg-dark">ل</span> دوام ليلي (12ل-8ص)</div>
                        <div class="mb-2"><span class="badge bg-info">ف</span> دوام فترتين (9ص-12ظ، 4م-9م)</div>
                    </div>
                </div>

                <div class="mt-3 d-flex justify-content-center">
                    {% if schedule_status == 'draft' %}
                    <a href="{{ url_for('direct_shift_swap') }}" class="btn btn-success me-2">
                        <i class="fas fa-exchange-alt me-1"></i>
                        تبديل دوام مباشر
                    </a>
                    {% else %}
                    <a href="{{ url_for('new_shift_swap') }}" class="btn btn-primary me-2">
                        <i class="fas fa-file-alt me-1"></i>
                        تقديم طلب تبديل
                    </a>
                    {% endif %}
                    <a href="{{ url_for('shift_swap_requests') }}" class="btn btn-info">
                        <i class="fas fa-list-alt me-1"></i>
                        طلبات التبديل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-chart-pie me-1"></i>
                توزيع أرصدة الإجازات
            </div>
            <div class="card-body chart-container">
                <canvas id="leaveBalanceChart" height="250"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-chart-bar me-1"></i>
                حالة الطلبات
            </div>
            <div class="card-body chart-container">
                <canvas id="requestStatusChart" height="250"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-calendar-alt me-1"></i>
                آخر طلبات الإجازة
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>النوع</th>
                                <th>من</th>
                                <th>إلى</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for leave in leave_requests %}
                            <tr>
                                <td>{{ leave.leave_type_name }}</td>
                                <td>{{ leave.start_date }}</td>
                                <td>{{ leave.end_date }}</td>
                                <td>
                                    {% if leave.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif leave.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif leave.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                            {% if not leave_requests %}
                            <tr>
                                <td colspan="4" class="text-center">لا توجد طلبات إجازة حتى الآن</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('my_leaves') }}" class="btn btn-primary btn-sm">عرض الكل</a>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-exchange-alt me-1"></i>
                طلبات التغطية
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>من</th>
                                <th>إلى</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for coverage in coverage_requests %}
                            <tr>
                                <td>{{ coverage.first_name }} {{ coverage.last_name }}</td>
                                <td>{{ coverage.coverage_date }}</td>
                                <td>{{ coverage.coverage_date }}</td>
                                <td>
                                    {% if coverage.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif coverage.status == 'approved' %}
                                    <span class="badge bg-success">تمت الموافقة</span>
                                    {% elif coverage.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                            {% if not coverage_requests %}
                            <tr>
                                <td colspan="4" class="text-center">لا توجد طلبات تغطية حتى الآن</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('my_coverage') }}" class="btn btn-primary btn-sm">عرض الكل</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // رسم بياني دائري - توزيع أرصدة الإجازات
        const leaveBalanceCtx = document.getElementById('leaveBalanceChart').getContext('2d');
        const leaveBalanceChart = new Chart(leaveBalanceCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    'إجازة اعتيادية',
                    'إجازة مرضية',
                    'إجازة اضطرارية',
                    'إجازة بدون راتب'
                ],
                datasets: [{
                    data: [
                        {{ chart_data.leave_balances.annual }},
                        {{ chart_data.leave_balances.sick }},
                        {{ chart_data.leave_balances.emergency }},
                        {{ chart_data.leave_balances.unpaid }}
                    ],
                    backgroundColor: [
                        '#4e73df',
                        '#1cc88a',
                        '#36b9cc',
                        '#f6c23e'
                    ],
                    hoverBackgroundColor: [
                        '#2e59d9',
                        '#17a673',
                        '#2c9faf',
                        '#dda20a'
                    ],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Tajawal'
                            },
                            color: '#000'
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.raw + ' يوم';
                            }
                        }
                    }
                },
                cutout: '70%',
                onClick: function(event, elements) {
                    if (elements.length > 0) {
                        const index = elements[0].index;
                        const labels = ['annual', 'sick', 'emergency', 'unpaid'];
                        const label = labels[index];

                        // توجيه المستخدم إلى صفحة أرصدة الإجازات
                        window.location.href = '{{ url_for("leave_balances") }}';
                    }
                }
            }
        });

        // رسم بياني شريطي - حالة الطلبات
        const requestStatusCtx = document.getElementById('requestStatusChart').getContext('2d');
        const requestStatusChart = new Chart(requestStatusCtx, {
            type: 'bar',
            data: {
                labels: ['طلبات الإجازة', 'طلبات التغطية', 'طلبات تبديل الدوام'],
                datasets: [{
                    label: 'قيد الانتظار',
                    data: [
                        {{ chart_data.request_status.pending.leaves }},
                        {{ chart_data.request_status.pending.coverage }},
                        {{ chart_data.request_status.pending.swaps }}
                    ],
                    backgroundColor: '#f6c23e'
                }, {
                    label: 'تمت الموافقة',
                    data: [
                        {{ chart_data.request_status.approved.leaves }},
                        {{ chart_data.request_status.approved.coverage }},
                        {{ chart_data.request_status.approved.swaps }}
                    ],
                    backgroundColor: '#1cc88a'
                }, {
                    label: 'مرفوض',
                    data: [
                        {{ chart_data.request_status.rejected.leaves }},
                        {{ chart_data.request_status.rejected.coverage }},
                        {{ chart_data.request_status.rejected.swaps }}
                    ],
                    backgroundColor: '#e74a3b'
                }]
            },
            options: {
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: {
                                family: 'Tajawal'
                            },
                            color: '#000'
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Tajawal'
                            },
                            color: '#000'
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                family: 'Tajawal'
                            },
                            color: '#000'
                        }
                    }
                },
                onClick: function(event, elements) {
                    if (elements.length > 0) {
                        const datasetIndex = elements[0].datasetIndex;
                        const index = elements[0].index;

                        // تحديد نوع الطلب والحالة
                        const requestTypes = ['my_leaves', 'my_coverage', 'shift_swap_requests'];
                        const statuses = ['pending', 'approved', 'rejected'];

                        const requestType = requestTypes[index];
                        const status = statuses[datasetIndex];

                        // توجيه المستخدم إلى الصفحة المناسبة
                        if (requestType === 'my_leaves') {
                            window.location.href = '{{ url_for("my_leaves") }}?status=' + status;
                        } else if (requestType === 'my_coverage') {
                            window.location.href = '{{ url_for("my_coverage") }}?status=' + status;
                        } else if (requestType === 'shift_swap_requests') {
                            window.location.href = '{{ url_for("shift_swap_requests") }}?status=' + status;
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}