import sqlite3
import os

def create_missing_tables():
    if not os.path.exists('alemis.db'):
        print("قاعدة البيانات غير موجودة!")
        return
    
    conn = sqlite3.connect('alemis.db')
    cursor = conn.cursor()
    
    try:
        # إنشاء جدول الجداول الشهرية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monthly_schedules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                department_id INTEGER NOT NULL,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                status TEXT DEFAULT 'draft',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                approved_at TIMESTAMP,
                approved_by INTEGER,
                FOREIGN KEY (department_id) REFERENCES departments (id),
                FOREIGN KEY (approved_by) REFERENCES users (id)
            )
        ''')
        print("✅ تم إنشاء جدول monthly_schedules")
        
        # إنشاء جدول تفاصيل الجداول الشهرية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS schedule_assignments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                schedule_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                date DATE NOT NULL,
                shift_type TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (schedule_id) REFERENCES monthly_schedules (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        print("✅ تم إنشاء جدول schedule_assignments")
        
        # إنشاء جدول أيام التغطية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS coverage_days (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                coverage_date DATE NOT NULL,
                coverage_type TEXT NOT NULL,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                used_at TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        print("✅ تم إنشاء جدول coverage_days")
        
        # إنشاء جدول الإشعارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                type TEXT DEFAULT 'info',
                is_read BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        print("✅ تم إنشاء جدول notifications")
        
        # إنشاء جدول سجل النشاطات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                action TEXT NOT NULL,
                description TEXT,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        print("✅ تم إنشاء جدول activity_log")
        
        # إنشاء جدول إعدادات النظام
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by INTEGER,
                FOREIGN KEY (updated_by) REFERENCES users (id)
            )
        ''')
        print("✅ تم إنشاء جدول system_settings")
        
        # إضافة بعض الإعدادات الافتراضية
        default_settings = [
            ('company_name', 'شركة العميس الطبية', 'اسم الشركة'),
            ('system_name', 'نظام إدارة موظفين المختبر', 'اسم النظام'),
            ('max_leave_days_per_year', '30', 'الحد الأقصى لأيام الإجازة السنوية'),
            ('max_sick_days_per_year', '15', 'الحد الأقصى لأيام الإجازة المرضية'),
            ('friday_coverage_morning', '1', 'عدد الموظفين للفترة الصباحية يوم الجمعة'),
            ('friday_coverage_evening', '2', 'عدد الموظفين للفترة المسائية يوم الجمعة'),
            ('friday_coverage_night', '1', 'عدد الموظفين للفترة الليلية يوم الجمعة')
        ]
        
        for key, value, desc in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO system_settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            ''', (key, value, desc))
        
        print("✅ تم إضافة الإعدادات الافتراضية")
        
        # التحقق من وجود الأعمدة المطلوبة في الجداول الموجودة
        
        # التحقق من جدول leave_requests
        cursor.execute('PRAGMA table_info(leave_requests)')
        leave_columns = [column[1] for column in cursor.fetchall()]
        
        if 'document_path' not in leave_columns:
            cursor.execute('ALTER TABLE leave_requests ADD COLUMN document_path TEXT')
            print("✅ تم إضافة عمود document_path إلى جدول leave_requests")
        
        if 'coverage_day_ids' not in leave_columns:
            cursor.execute('ALTER TABLE leave_requests ADD COLUMN coverage_day_ids TEXT')
            print("✅ تم إضافة عمود coverage_day_ids إلى جدول leave_requests")
        
        # التحقق من جدول shift_swap_requests
        cursor.execute('PRAGMA table_info(shift_swap_requests)')
        swap_columns = [column[1] for column in cursor.fetchall()]
        
        if 'requester_shift_type' not in swap_columns:
            cursor.execute('ALTER TABLE shift_swap_requests ADD COLUMN requester_shift_type TEXT')
            print("✅ تم إضافة عمود requester_shift_type إلى جدول shift_swap_requests")
        
        if 'multiple_days' not in swap_columns:
            cursor.execute('ALTER TABLE shift_swap_requests ADD COLUMN multiple_days TEXT')
            print("✅ تم إضافة عمود multiple_days إلى جدول shift_swap_requests")
        
        # التحقق من جدول coverage_requests
        cursor.execute('PRAGMA table_info(coverage_requests)')
        coverage_columns = [column[1] for column in cursor.fetchall()]
        
        if 'manager_comment' not in coverage_columns:
            cursor.execute('ALTER TABLE coverage_requests ADD COLUMN manager_comment TEXT')
            print("✅ تم إضافة عمود manager_comment إلى جدول coverage_requests")
        
        if 'hr_comment' not in coverage_columns:
            cursor.execute('ALTER TABLE coverage_requests ADD COLUMN hr_comment TEXT')
            print("✅ تم إضافة عمود hr_comment إلى جدول coverage_requests")
        
        if 'hospital_manager_comment' not in coverage_columns:
            cursor.execute('ALTER TABLE coverage_requests ADD COLUMN hospital_manager_comment TEXT')
            print("✅ تم إضافة عمود hospital_manager_comment إلى جدول coverage_requests")
        
        conn.commit()
        print("\n🎉 تم إنشاء وتحديث جميع الجداول بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    create_missing_tables()
